# Market Simulation Usage Guide

## Quick Start

The simplest way to run the market simulation is:

```bash
python market_sim/run.py
```

This will start the web server on `http://127.0.0.1:8521/` with all Phase 2C features enabled.

## Prerequisites

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Gemini API Key (Optional but Recommended)

1. Get a free API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a `.env` file in the project root:
   ```
   GEMINI_API_KEY=your_api_key_here
   ```

**Note**: Without an API key, smart agents will automatically fall back to basic agent logic.

## Command Line Options

### Basic Usage
```bash
python market_sim/run.py [OPTIONS]
```

### Available Options

| Option | Description | Default |
|--------|-------------|---------|
| `--port PORT` | Port to run the server on | 8521 |
| `--host HOST` | Host to bind the server to | 127.0.0.1 |
| `--debug` | Run in debug mode with detailed logging | False |
| `--no-api-check` | Skip API key validation at startup | False |
| `--help` | Show help message and exit | - |

### Examples

**Run on a different port:**
```bash
python market_sim/run.py --port 8080
```

**Run in debug mode:**
```bash
python market_sim/run.py --debug
```

**Run without API key check:**
```bash
python market_sim/run.py --no-api-check
```

**Run on all interfaces:**
```bash
python market_sim/run.py --host 0.0.0.0 --port 8080
```

## Web Interface Features

Once the server is running, open your browser to the displayed URL (default: `http://127.0.0.1:8521/`)

### 🎮 Simulation Controls
- **Start/Stop**: Control simulation execution
- **Step**: Execute one simulation step
- **Reset**: Reset with new parameters

### ⚙️ Market Parameters
- **Basic Agents**: Number of traditional trading agents (1-50)
- **Smart Agents**: Number of AI-powered agents (0-20)
- **Grid Size**: Simulation space dimensions (10x10 to 30x30)
- **Initial Price**: Starting asset price ($50-$200)
- **Initial Wealth**: Starting cash for agents ($500-$2000)

### 🎛️ Speed & API Controls
- **Simulation Speed**: Adjust step frequency (0.01s to 5s per step)
- **API Calls/Min**: Rate limiting for Gemini API (5-60 calls/minute)
- **Min API Delay**: Minimum delay between API calls (0.5s to 10s)
- **Enable Caching**: Toggle decision caching for efficiency

### 📊 Real-time Monitoring
- **Agent Grid**: Visual representation of agents and their actions
  - Blue circles: Basic agents
  - Red circles: Smart agents
  - Green: Currently buying
  - Orange: Currently selling
  - Size: Proportional to wealth
- **Price Chart**: Real-time market price movements
- **Volume Chart**: Transaction volume over time
- **Wealth Comparison**: Performance comparison between agent types

### 🤖 AI Agent Status
- **API Usage**: Real-time API call statistics
- **Success Rate**: Percentage of successful API calls
- **Cache Hits**: Number of cached decisions used
- **Quota Status**: Current usage vs. rate limits
- **Wait Times**: Time until next API call allowed

## Phase 2C Features

### Smart Agents with AI Integration
- **Gemini API Integration**: Uses Google's Gemini 2.0 Flash for decision making
- **Context-Aware Decisions**: Analyzes market conditions, trends, and personal state
- **System Instructions**: Efficient prompt design reduces token usage
- **Fallback Behavior**: Automatically switches to basic logic when API fails

### Rate Limiting & Quota Management
- **Global Rate Limiter**: Prevents API quota exhaustion
- **Configurable Limits**: Adjustable rate limits and delays
- **Intelligent Caching**: Reduces redundant API calls by 40-60%
- **Real-time Monitoring**: Live tracking of API usage and quotas

### Advanced Controls
- **Speed Control**: Real-time simulation speed adjustment
- **API Configuration**: Live rate limiter settings adjustment
- **Performance Metrics**: Comprehensive tracking of all agent types
- **Error Recovery**: Graceful handling of API failures and quota limits

## Troubleshooting

### Common Issues

**Port Already in Use**
```
Address already in use
Port 8521 is in use by another program.
```
**Solution**: Use a different port with `--port 8522`

**Missing Dependencies**
```
❌ Missing required dependencies: flask
```
**Solution**: Install dependencies with `pip install -r requirements.txt`

**API Key Issues**
```
⚠️ Warning: GEMINI_API_KEY not found in environment.
```
**Solution**: 
1. Create a `.env` file in the project root
2. Add your API key: `GEMINI_API_KEY=your_key_here`
3. Or run with `--no-api-check` to skip validation

**Import Errors**
```
❌ Import error: No module named 'market_sim'
```
**Solution**: Make sure you're running from the project root directory

### Performance Tips

1. **Optimize API Usage**: Use slower simulation speeds (1-2s per step) to stay within rate limits
2. **Enable Caching**: Keep caching enabled to reduce API calls
3. **Monitor Quotas**: Watch the API status display to avoid quota exhaustion
4. **Adjust Agent Mix**: Start with fewer smart agents (2-5) to test API integration

### Debug Mode

Run with `--debug` for detailed logging:
```bash
python market_sim/run.py --debug
```

This provides:
- Detailed Flask request logging
- API call debugging information
- Rate limiter status updates
- Agent decision tracking

## Next Steps

After successfully running the simulation:

1. **Experiment with Parameters**: Try different agent ratios and market conditions
2. **Monitor Performance**: Compare smart vs. basic agent performance
3. **Optimize Settings**: Adjust rate limiting and caching for your use case
4. **Explore API Usage**: Monitor how different settings affect API consumption

For advanced usage and development, see the project documentation and Phase 3 planning.
