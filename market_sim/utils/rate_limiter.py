"""
API Rate Limiter for managing Gemini API calls within quota limits.

This module provides a centralized rate limiting mechanism to prevent
API quota exhaustion while maintaining simulation performance.
"""

import time
import threading
from typing import Dict, Any, Optional
from collections import deque
from dataclasses import dataclass
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class APICallRecord:
    """Record of an API call for rate limiting purposes."""
    timestamp: float
    agent_id: int
    success: bool
    tokens_used: int = 0


class APIRateLimiter:
    """
    Global API rate limiter for managing Gemini API calls.
    
    Features:
    - Enforces rate limits (15 requests per minute for free tier)
    - Tracks API usage statistics
    - Provides intelligent queuing and throttling
    - Caches recent decisions to reduce API calls
    """
    
    def __init__(
        self,
        max_requests_per_minute: int = 15,
        min_delay_between_calls: float = 2.0,
        cache_duration: int = 5,
        enable_caching: bool = True
    ):
        """
        Initialize the rate limiter.
        
        Args:
            max_requests_per_minute: Maximum API calls per minute (Gemini free tier: 15)
            min_delay_between_calls: Minimum seconds between API calls
            cache_duration: How long to cache decisions (steps)
            enable_caching: Whether to enable decision caching
        """
        self.max_requests_per_minute = max_requests_per_minute
        self.min_delay_between_calls = min_delay_between_calls
        self.cache_duration = cache_duration
        self.enable_caching = enable_caching
        
        # Rate limiting state
        self._call_history = deque(maxlen=100)  # Keep last 100 calls
        self._last_call_time = 0.0
        self._lock = threading.Lock()
        
        # Decision cache: {agent_id: {context_hash: (decision, confidence, reasoning, timestamp)}}
        self._decision_cache = {}
        
        # Statistics
        self.total_calls_made = 0
        self.total_calls_blocked = 0
        self.total_cache_hits = 0
        self.total_tokens_used = 0
        
        logger.info(f"APIRateLimiter initialized: {max_requests_per_minute} req/min, {min_delay_between_calls}s delay")
    
    def can_make_call(self) -> tuple[bool, float]:
        """
        Check if an API call can be made now.
        
        Returns:
            Tuple of (can_call, wait_time_seconds)
        """
        with self._lock:
            current_time = time.time()
            
            # Check minimum delay between calls
            time_since_last = current_time - self._last_call_time
            if time_since_last < self.min_delay_between_calls:
                wait_time = self.min_delay_between_calls - time_since_last
                return False, wait_time
            
            # Check rate limit (calls per minute)
            minute_ago = current_time - 60
            recent_calls = [call for call in self._call_history if call.timestamp > minute_ago]
            
            if len(recent_calls) >= self.max_requests_per_minute:
                # Find when the oldest call in the window will expire
                oldest_call_time = min(call.timestamp for call in recent_calls)
                wait_time = (oldest_call_time + 60) - current_time
                return False, max(wait_time, 0)
            
            return True, 0.0
    
    def wait_for_availability(self, timeout: float = 30.0) -> bool:
        """
        Wait until an API call can be made.
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if call can be made, False if timeout reached
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            can_call, wait_time = self.can_make_call()
            if can_call:
                return True
            
            # Wait for the shorter of wait_time or remaining timeout
            remaining_timeout = timeout - (time.time() - start_time)
            sleep_time = min(wait_time, remaining_timeout, 1.0)  # Max 1 second sleep
            
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        return False
    
    def record_call(self, agent_id: int, success: bool, tokens_used: int = 0):
        """
        Record an API call for rate limiting purposes.
        
        Args:
            agent_id: ID of the agent making the call
            success: Whether the call was successful
            tokens_used: Number of tokens used (if available)
        """
        with self._lock:
            current_time = time.time()
            
            record = APICallRecord(
                timestamp=current_time,
                agent_id=agent_id,
                success=success,
                tokens_used=tokens_used
            )
            
            self._call_history.append(record)
            self._last_call_time = current_time
            self.total_calls_made += 1
            self.total_tokens_used += tokens_used
            
            if not success:
                logger.warning(f"API call failed for agent {agent_id}")
    
    def get_cached_decision(self, agent_id: int, context_hash: str, current_step: int) -> Optional[tuple]:
        """
        Get a cached decision if available and still valid.
        
        Args:
            agent_id: ID of the agent
            context_hash: Hash of the market context
            current_step: Current simulation step
            
        Returns:
            Tuple of (decision, confidence, reasoning) if cached, None otherwise
        """
        if not self.enable_caching:
            return None
        
        agent_cache = self._decision_cache.get(agent_id, {})
        cached_entry = agent_cache.get(context_hash)
        
        if cached_entry:
            decision, confidence, reasoning, cached_step = cached_entry
            if current_step - cached_step <= self.cache_duration:
                self.total_cache_hits += 1
                logger.debug(f"Cache hit for agent {agent_id}")
                return decision, confidence, reasoning
        
        return None
    
    def cache_decision(self, agent_id: int, context_hash: str, decision: str, 
                      confidence: float, reasoning: str, current_step: int):
        """
        Cache a decision for future use.
        
        Args:
            agent_id: ID of the agent
            context_hash: Hash of the market context
            decision: The decision made
            confidence: Confidence level
            reasoning: Reasoning for the decision
            current_step: Current simulation step
        """
        if not self.enable_caching:
            return
        
        if agent_id not in self._decision_cache:
            self._decision_cache[agent_id] = {}
        
        self._decision_cache[agent_id][context_hash] = (decision, confidence, reasoning, current_step)
        
        # Clean old cache entries (keep only recent ones)
        agent_cache = self._decision_cache[agent_id]
        valid_entries = {
            k: v for k, v in agent_cache.items() 
            if current_step - v[3] <= self.cache_duration * 2  # Keep a bit longer for cleanup
        }
        self._decision_cache[agent_id] = valid_entries
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        Get current API usage statistics.
        
        Returns:
            Dictionary with usage statistics
        """
        with self._lock:
            current_time = time.time()
            minute_ago = current_time - 60
            recent_calls = [call for call in self._call_history if call.timestamp > minute_ago]
            successful_calls = [call for call in recent_calls if call.success]
            
            return {
                "total_calls_made": self.total_calls_made,
                "total_calls_blocked": self.total_calls_blocked,
                "total_cache_hits": self.total_cache_hits,
                "total_tokens_used": self.total_tokens_used,
                "calls_last_minute": len(recent_calls),
                "successful_calls_last_minute": len(successful_calls),
                "max_calls_per_minute": self.max_requests_per_minute,
                "cache_hit_rate": self.total_cache_hits / max(1, self.total_calls_made + self.total_cache_hits),
                "can_make_call_now": self.can_make_call()[0],
                "time_until_next_call": self.can_make_call()[1]
            }
    
    def reset_stats(self):
        """Reset all statistics."""
        with self._lock:
            self.total_calls_made = 0
            self.total_calls_blocked = 0
            self.total_cache_hits = 0
            self.total_tokens_used = 0
            self._call_history.clear()
            self._decision_cache.clear()
            logger.info("Rate limiter statistics reset")


# Global rate limiter instance
_global_rate_limiter = None


def get_rate_limiter() -> APIRateLimiter:
    """Get the global rate limiter instance."""
    global _global_rate_limiter
    if _global_rate_limiter is None:
        _global_rate_limiter = APIRateLimiter()
    return _global_rate_limiter


def configure_rate_limiter(**kwargs) -> APIRateLimiter:
    """Configure the global rate limiter with custom settings."""
    global _global_rate_limiter
    _global_rate_limiter = APIRateLimiter(**kwargs)
    return _global_rate_limiter
