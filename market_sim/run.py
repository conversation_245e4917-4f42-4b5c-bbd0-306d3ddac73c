"""
Entry point for running the market simulation visualization.

This script launches the Mesa visualization server, allowing users to:
- Interact with the simulation through a web browser
- Adjust parameters in real-time
- View live charts and agent behavior
- Control simulation speed and steps
"""

import os
import sys
from dotenv import load_dotenv

def main():
    """Launch the visualization server."""
    # Add current directory to Python path
    import sys
    import os
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # Load environment variables
    load_dotenv()

    # Check if API key is available
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("⚠️  Warning: GEMINI_API_KEY not found in environment.")
        print("   Smart agents will not function without an API key.")
        print("   You can still run basic agents for testing.")
        print()

    print("🚀 Starting Market Simulation Visualization Server...")
    print("📊 Features available:")
    print("   - Interactive grid with agent visualization")
    print("   - Real-time price and volume charts")
    print("   - Adjustable simulation parameters")
    print("   - Agent wealth tracking")
    print()
    print("🌐 Server will be available at: http://127.0.0.1:8521/")
    print("💡 Tip: Adjust parameters and click 'Reset' to see different scenarios")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)

    try:
        # Import to verify everything works
        from market_sim.visualization.server import page
        print("✅ Visualization components loaded successfully")
        print()

        # Launch with Solara command line
        import subprocess
        print("🚀 Launching Solara server...")
        print("   Use Ctrl+C to stop the server")
        print()

        # Run solara command
        result = subprocess.run([
            sys.executable, "-m", "solara", "run",
            "market_sim.visualization.server:page",
            "--host", "127.0.0.1",
            "--port", "8521"
        ], cwd=current_dir)

    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the project root directory")
        print("Also ensure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
