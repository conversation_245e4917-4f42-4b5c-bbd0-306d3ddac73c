"""
Entry point for running the market simulation with smart agents and advanced features.

This script launches the Flask-based web server with Phase 2C features:
- Smart agents with Gemini API integration
- Rate limiting and API quota management
- Real-time speed controls and monitoring
- Interactive web visualization with modern UI
- Comprehensive API usage statistics
"""

import os
import sys
import argparse
from dotenv import load_dotenv

def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []

    try:
        import flask
    except ImportError:
        missing_deps.append("flask")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")

    try:
        from google import genai
    except ImportError:
        missing_deps.append("google-genai")

    try:
        import mesa
    except ImportError:
        missing_deps.append("mesa")

    if missing_deps:
        print("❌ Missing required dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Install missing dependencies with:")
        print("   pip install " + " ".join(missing_deps))
        return False

    return True

def check_environment():
    """Check environment setup and API key."""
    # Load environment variables
    load_dotenv()

    # Check if API key is available
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("⚠️  Warning: GEMINI_API_KEY not found in environment.")
        print("   Smart agents will fall back to basic agent logic.")
        print("   To enable AI features:")
        print("   1. Get a Gemini API key from https://makersuite.google.com/app/apikey")
        print("   2. Create a .env file in the project root")
        print("   3. Add: GEMINI_API_KEY=your_api_key_here")
        print()
        return False
    else:
        print(f"✅ Gemini API key found: {api_key[:10]}...")
        return True

def main():
    """Launch the market simulation web server."""
    parser = argparse.ArgumentParser(description="Market Simulation with Smart Agents")
    parser.add_argument("--port", type=int, default=8521, help="Port to run the server on (default: 8521)")
    parser.add_argument("--host", default="127.0.0.1", help="Host to bind the server to (default: 127.0.0.1)")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    parser.add_argument("--no-api-check", action="store_true", help="Skip API key check")
    args = parser.parse_args()

    print("🚀 Market Simulation with Smart Agents - Phase 2C")
    print("=" * 60)

    # Check dependencies
    if not check_dependencies():
        sys.exit(1)

    # Check environment (unless skipped)
    api_available = True
    if not args.no_api_check:
        api_available = check_environment()

    print("\n📊 Phase 2C Features Available:")
    print("   ✅ Smart agents with Gemini API integration")
    print("   ✅ Rate limiting and API quota management")
    print("   ✅ Real-time simulation speed controls")
    print("   ✅ Interactive web visualization")
    print("   ✅ Live API usage monitoring")
    print("   ✅ Decision caching for efficiency")
    print("   ✅ Graceful fallback behavior")

    if api_available:
        print("   🤖 AI agents: ENABLED")
    else:
        print("   🤖 AI agents: DISABLED (will use basic agent logic)")

    print(f"\n🌐 Server will be available at: http://{args.host}:{args.port}/")
    print("💡 Features:")
    print("   - Adjust simulation speed with the slider")
    print("   - Monitor API usage in real-time")
    print("   - Configure rate limiting settings")
    print("   - Watch smart agents (red) vs basic agents (blue)")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)

    try:
        # Add current directory to Python path
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        # Import and start the Flask web server
        from market_sim.visualization.web_server import app, create_model

        print("✅ Loading market simulation components...")

        # Skip initial model creation to avoid threading issues
        # The model will be created when the web interface is first accessed
        print("✅ Market simulation components loaded")
        print("✅ Rate limiting system ready")
        print("ℹ️  Model will be initialized on first web access")
        print()

        print("🚀 Starting Flask web server...")
        print(f"   Host: {args.host}")
        print(f"   Port: {args.port}")
        print(f"   Debug: {args.debug}")
        print("   Starting server now...")

        # Start the Flask server
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            threaded=True,
            use_reloader=False  # Disable reloader to avoid issues
        )

    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        print("Thank you for using the Market Simulation!")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the project root directory")
        print("Also ensure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check that port is not already in use")
        print("2. Verify all dependencies are installed")
        print("3. Ensure you're in the project root directory")
        print("4. Check the .env file for API key configuration")
        sys.exit(1)

if __name__ == "__main__":
    main()
