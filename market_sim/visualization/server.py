"""
Mesa Solara visualization for the market simulation.

This module creates an interactive web-based visualization using Mesa 3.x's Solara system:
- Grid with agents colored by type and current action
- Real-time price chart
- Transaction volume chart
- Agent wealth comparison
- Interactive controls for simulation parameters
"""

import solara
from mesa.visualization import SolaraViz, make_space_component, make_plot_component
from market_sim.model import MarketModel


def agent_portrayal(agent):
    """
    Define how agents are displayed on the grid for Solara visualization.

    Args:
        agent: The agent to portray

    Returns:
        dict: Portrayal dictionary for Mesa Solara visualization
    """
    # Base color based on agent type
    if agent.agent_type == "basic":
        color = "#3498db"  # Blue for basic agents
    elif agent.agent_type == "smart":
        color = "#e74c3c"  # Red for smart agents
    else:
        color = "#95a5a6"  # Gray for unknown types

    # Modify color based on last action
    if hasattr(agent, 'last_action'):
        if agent.last_action == "buy":
            color = "#27ae60"  # Green for buyers
        elif agent.last_action == "sell":
            color = "#e67e22"  # Orange for sellers

    # Size based on wealth
    wealth_ratio = agent.wealth / 1000.0  # Assuming 1000 is starting wealth
    size = max(10, min(30, wealth_ratio * 20))

    return {
        "color": color,
        "size": size,
        "marker": "o",  # Circle marker
        "alpha": max(0.5, min(1.0, wealth_ratio))
    }


# Create model parameters for Solara
model_params = {
    "num_basic_agents": {
        "type": "SliderInt",
        "value": 15,
        "label": "Number of Basic Agents",
        "min": 5,
        "max": 50,
        "step": 1,
    },
    "num_smart_agents": {
        "type": "SliderInt",
        "value": 5,
        "label": "Number of Smart Agents",
        "min": 0,
        "max": 20,
        "step": 1,
    },
    "width": {
        "type": "SliderInt",
        "value": 20,
        "label": "Grid Width",
        "min": 10,
        "max": 30,
        "step": 1,
    },
    "height": {
        "type": "SliderInt",
        "value": 20,
        "label": "Grid Height",
        "min": 10,
        "max": 30,
        "step": 1,
    },
    "initial_price": {
        "type": "SliderFloat",
        "value": 100.0,
        "label": "Initial Price",
        "min": 50.0,
        "max": 200.0,
        "step": 5.0,
    },
    "initial_wealth": {
        "type": "SliderFloat",
        "value": 1000.0,
        "label": "Initial Wealth",
        "min": 500.0,
        "max": 2000.0,
        "step": 100.0,
    },
    "initial_inventory": {
        "type": "SliderInt",
        "value": 10,
        "label": "Initial Inventory",
        "min": 5,
        "max": 20,
        "step": 1,
    },
}

# Create the Solara visualization
def create_visualization():
    """Create and return the Solara visualization."""

    # Create space component (grid visualization)
    space_component = make_space_component(agent_portrayal)

    # Create plot components for charts
    price_component = make_plot_component("Price")
    volume_component = make_plot_component("Transaction_Volume")
    wealth_basic_component = make_plot_component("Avg_Wealth_Basic")
    wealth_smart_component = make_plot_component("Avg_Wealth_Smart")

    # Create the SolaraViz instance
    page = SolaraViz(
        MarketModel,
        components=[
            space_component,
            price_component,
            volume_component,
            wealth_basic_component,
            wealth_smart_component,
        ],
        model_params=model_params,
        name="Market Simulation with AI Agents",
    )

    return page

# Create the visualization instance
page = create_visualization()
