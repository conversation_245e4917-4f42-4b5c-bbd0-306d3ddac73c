<!DOCTYPE html>
<html>
<head>
    <title>Market Simulation - Simple View</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-step { background: #007bff; color: white; }
        .btn-reset { background: #28a745; color: white; }
        .btn-step:hover { background: #0056b3; }
        .btn-reset:hover { background: #1e7e34; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .agent-grid { width: 100%; height: 300px; border: 1px solid #ddd; position: relative; background: #f8f9fa; }
        .agent { position: absolute; border-radius: 50%; border: 2px solid white; }
        .agent.basic { background: #007bff; }
        .agent.smart { background: #dc3545; }
        .stats { margin-top: 10px; }
        .stat { margin: 5px 0; padding: 5px; background: #f8f9fa; border-radius: 4px; }
        .chart-area { height: 200px; border: 1px solid #ddd; background: white; position: relative; overflow: hidden; }
        .bar { position: absolute; bottom: 0; background: #28a745; opacity: 0.7; }
        .line-point { position: absolute; width: 4px; height: 4px; background: #007bff; border-radius: 50%; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Market Simulation - Simple View</h1>
            <p>Lightweight visualization for agent-based market simulation</p>
        </div>
        
        <div class="controls">
            <button class="button btn-step" onclick="stepSimulation()">▶️ Step</button>
            <button class="button btn-reset" onclick="resetSimulation()">🔄 Reset</button>
            <span id="status">Ready</span>
        </div>
        
        <div class="grid">
            <div class="panel">
                <h3>🎯 Agent Grid</h3>
                <div class="agent-grid" id="agentGrid"></div>
                <div class="stats">
                    <div class="stat">Step: <span id="stepCount">0</span></div>
                    <div class="stat">Agents: <span id="agentCount">0</span></div>
                </div>
            </div>
            
            <div class="panel">
                <h3>💰 Market Price</h3>
                <div class="chart-area" id="priceChart"></div>
                <div class="stats">
                    <div class="stat">Current Price: $<span id="currentPrice">0.00</span></div>
                    <div class="stat">Volume: <span id="currentVolume">0</span></div>
                </div>
            </div>
            
            <div class="panel">
                <h3>📊 Transaction Volume</h3>
                <div class="chart-area" id="volumeChart"></div>
                <div class="stats">
                    <div class="stat">Total Transactions: <span id="totalTransactions">0</span></div>
                </div>
            </div>
            
            <div class="panel">
                <h3>💎 Agent Wealth</h3>
                <div class="chart-area" id="wealthChart"></div>
                <div class="stats">
                    <div class="stat">Avg Wealth: $<span id="avgWealth">0.00</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentData = null;
        
        function updateDisplay() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    currentData = data;
                    updateAgentGrid(data);
                    updateStats(data);
                    updateCharts(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = 'Error loading data';
                });
        }
        
        function updateAgentGrid(data) {
            const grid = document.getElementById('agentGrid');
            grid.innerHTML = '';
            
            if (!data.agents) return;
            
            const gridWidth = grid.offsetWidth;
            const gridHeight = grid.offsetHeight;
            
            data.agents.forEach(agent => {
                const agentDiv = document.createElement('div');
                agentDiv.className = `agent ${agent.type}`;
                
                // Position based on grid coordinates (assuming 15x15 grid)
                const x = (agent.x / 15) * gridWidth;
                const y = (agent.y / 15) * gridHeight;
                
                // Size based on wealth
                const size = Math.max(8, Math.min(20, agent.wealth / 80));
                
                agentDiv.style.left = x + 'px';
                agentDiv.style.top = y + 'px';
                agentDiv.style.width = size + 'px';
                agentDiv.style.height = size + 'px';
                agentDiv.title = `Agent ${agent.id}: $${agent.wealth.toFixed(0)} (${agent.inventory} items)`;
                
                grid.appendChild(agentDiv);
            });
        }
        
        function updateStats(data) {
            document.getElementById('stepCount').textContent = data.current_step || 0;
            document.getElementById('agentCount').textContent = data.agents ? data.agents.length : 0;
            document.getElementById('currentPrice').textContent = data.prices && data.prices.length > 0 ? 
                data.prices[data.prices.length - 1].toFixed(2) : '0.00';
            document.getElementById('currentVolume').textContent = data.volumes && data.volumes.length > 0 ? 
                data.volumes[data.volumes.length - 1] : 0;
            document.getElementById('avgWealth').textContent = data.wealth_basic && data.wealth_basic.length > 0 ? 
                data.wealth_basic[data.wealth_basic.length - 1].toFixed(2) : '0.00';
            
            const totalTransactions = data.volumes ? data.volumes.reduce((a, b) => a + b, 0) : 0;
            document.getElementById('totalTransactions').textContent = totalTransactions;
        }
        
        function updateCharts(data) {
            updatePriceChart(data);
            updateVolumeChart(data);
            updateWealthChart(data);
        }
        
        function updatePriceChart(data) {
            const chart = document.getElementById('priceChart');
            chart.innerHTML = '';
            
            if (!data.prices || data.prices.length < 2) return;
            
            const maxPrice = Math.max(...data.prices);
            const minPrice = Math.min(...data.prices);
            const priceRange = maxPrice - minPrice || 1;
            
            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;
            
            data.prices.forEach((price, index) => {
                if (index === 0) return;
                
                const x = (index / (data.prices.length - 1)) * chartWidth;
                const y = chartHeight - ((price - minPrice) / priceRange) * chartHeight;
                
                const point = document.createElement('div');
                point.className = 'line-point';
                point.style.left = x + 'px';
                point.style.top = y + 'px';
                point.title = `Step ${index}: $${price.toFixed(2)}`;
                
                chart.appendChild(point);
            });
        }
        
        function updateVolumeChart(data) {
            const chart = document.getElementById('volumeChart');
            chart.innerHTML = '';
            
            if (!data.volumes || data.volumes.length === 0) return;
            
            const maxVolume = Math.max(...data.volumes) || 1;
            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;
            
            const recentVolumes = data.volumes.slice(-10); // Show last 10 steps
            
            recentVolumes.forEach((volume, index) => {
                const barWidth = chartWidth / recentVolumes.length;
                const barHeight = (volume / maxVolume) * chartHeight;
                
                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.left = (index * barWidth) + 'px';
                bar.style.width = (barWidth - 2) + 'px';
                bar.style.height = barHeight + 'px';
                bar.title = `Volume: ${volume}`;
                
                chart.appendChild(bar);
            });
        }
        
        function updateWealthChart(data) {
            const chart = document.getElementById('wealthChart');
            chart.innerHTML = '';
            
            if (!data.wealth_basic || data.wealth_basic.length < 2) return;
            
            const maxWealth = Math.max(...data.wealth_basic);
            const minWealth = Math.min(...data.wealth_basic);
            const wealthRange = maxWealth - minWealth || 1;
            
            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;
            
            data.wealth_basic.forEach((wealth, index) => {
                if (index === 0) return;
                
                const x = (index / (data.wealth_basic.length - 1)) * chartWidth;
                const y = chartHeight - ((wealth - minWealth) / wealthRange) * chartHeight;
                
                const point = document.createElement('div');
                point.className = 'line-point';
                point.style.left = x + 'px';
                point.style.top = y + 'px';
                point.style.background = '#28a745';
                point.title = `Step ${index}: $${wealth.toFixed(2)}`;
                
                chart.appendChild(point);
            });
        }
        
        function stepSimulation() {
            document.getElementById('status').textContent = 'Running step...';
            
            fetch('/api/step')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').textContent = 'Step completed';
                    updateDisplay();
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = 'Error stepping simulation';
                });
        }
        
        function resetSimulation() {
            document.getElementById('status').textContent = 'Resetting...';
            
            fetch('/api/reset')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').textContent = 'Reset completed';
                    updateDisplay();
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('status').textContent = 'Error resetting simulation';
                });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            resetSimulation();
            
            // Auto-update every 2 seconds
            setInterval(updateDisplay, 2000);
        });
    </script>
</body>
</html>