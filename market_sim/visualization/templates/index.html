<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Simulation Visualization</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            --gradient-info: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--dark-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
            animation: fadeInDown 0.8s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-xl);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .controls {
            padding: 25px;
            margin-bottom: 25px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .control-section {
            margin-bottom: 20px;
        }

        .control-section h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            color: white;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-start { background: var(--gradient-success); }
        .btn-stop { background: var(--gradient-danger); }
        .btn-step { background: var(--gradient-info); }
        .btn-reset { background: var(--gradient-warning); }

        .parameters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
        }

        .param-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-size: 0.9rem;
        }

        .param-group input {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        .param-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .status {
            padding: 20px 25px;
            margin-bottom: 25px;
            animation: fadeInUp 0.8s ease-out 0.4s both;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.95rem;
            background: rgba(255, 255, 255, 0.98);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-running { background-color: var(--success-color); }
        .status-stopped { background-color: var(--danger-color); }

        .charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .chart {
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        .chart::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .chart h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: var(--primary-color);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(102, 126, 234, 0.1);
            border-left: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }

        .transaction-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: var(--success-color);
            border-radius: 50%;
            pointer-events: none;
            animation: particle-float 2s ease-out forwards;
        }

        @keyframes particle-float {
            0% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            100% {
                opacity: 0;
                transform: scale(0.5) translateY(-50px);
            }
        }

        .chart-container {
            position: relative;
            height: 350px;
        }

        /* Simple chart styles */
        .agent-grid {
            width: 100%;
            height: 100%;
            border: 1px solid #ddd;
            position: relative;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .agent {
            position: absolute;
            border-radius: 50%;
            border: 2px solid white;
            transition: all 0.3s ease;
        }
        .agent.basic { background: #3b82f6; }
        .agent.smart { background: #ef4444; }
        .agent.buying { background: #10b981; }
        .agent.selling { background: #f59e0b; }

        .simple-chart {
            height: 100%;
            border: 1px solid #ddd;
            background: white;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
        }
        .bar {
            position: absolute;
            bottom: 0;
            background: #10b981;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        .line-point {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #667eea;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .chart-stats {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .metric-card {
            background: var(--gradient-primary);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 10px 0;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: scale(1.05);
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .charts {
                grid-template-columns: 1fr;
            }

            .button-group {
                justify-content: center;
            }

            .parameters {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> Market Simulation Visualization</h1>
            <p>Interactive agent-based market simulation with real-time AI-powered trading</p>
        </div>

        <div class="glass-card controls">
            <div class="control-section">
                <h3><i class="fas fa-gamepad"></i> Simulation Controls</h3>
                <div class="button-group">
                    <button class="btn btn-start" onclick="startSimulation()">
                        <i class="fas fa-play"></i> Start
                    </button>
                    <button class="btn btn-stop" onclick="stopSimulation()">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                    <button class="btn btn-step" onclick="stepSimulation()">
                        <i class="fas fa-step-forward"></i> Step
                    </button>
                    <button class="btn btn-reset" onclick="resetSimulation()">
                        <i class="fas fa-redo"></i> Reset
                    </button>
                </div>
            </div>

            <div class="control-section">
                <h3><i class="fas fa-sliders-h"></i> Market Parameters</h3>
                <div class="parameters">
                    <div class="param-group">
                        <label><i class="fas fa-users"></i> Basic Agents:</label>
                        <input type="number" id="num_basic_agents" value="20" min="1" max="50">
                    </div>
                    <div class="param-group">
                        <label><i class="fas fa-robot"></i> Smart Agents:</label>
                        <input type="number" id="num_smart_agents" value="0" min="0" max="20">
                    </div>
                    <div class="param-group">
                        <label><i class="fas fa-expand-arrows-alt"></i> Grid Width:</label>
                        <input type="number" id="width" value="15" min="10" max="30">
                    </div>
                    <div class="param-group">
                        <label><i class="fas fa-arrows-alt-v"></i> Grid Height:</label>
                        <input type="number" id="height" value="15" min="10" max="30">
                    </div>
                    <div class="param-group">
                        <label><i class="fas fa-dollar-sign"></i> Initial Price:</label>
                        <input type="number" id="initial_price" value="100" min="50" max="200" step="5">
                    </div>
                    <div class="param-group">
                        <label><i class="fas fa-wallet"></i> Initial Wealth:</label>
                        <input type="number" id="initial_wealth" value="1000" min="500" max="2000" step="100">
                    </div>
                </div>
            </div>
        </div>

        <div class="glass-card status" id="status">
            <span class="status-indicator status-stopped"></span>
            <span id="status-text">Status: Ready | Step: 0 | Agents: 0 | Price: $0.00 | Volume: 0</span>
        </div>

        <div class="charts">
            <div class="glass-card chart full-width">
                <h3><i class="fas fa-th"></i> Agent Grid</h3>
                <div class="chart-container">
                    <div class="agent-grid" id="agentGrid"></div>
                    <div class="chart-stats" id="gridStats">Agents: 0</div>
                </div>
            </div>
            <div class="glass-card chart">
                <h3><i class="fas fa-chart-line"></i> Market Price</h3>
                <div class="chart-container">
                    <div class="simple-chart" id="priceChart"></div>
                    <div class="chart-stats" id="priceStats">$0.00</div>
                </div>
            </div>
            <div class="glass-card chart">
                <h3><i class="fas fa-chart-bar"></i> Transaction Volume</h3>
                <div class="chart-container">
                    <div class="simple-chart" id="volumeChart"></div>
                    <div class="chart-stats" id="volumeStats">0 transactions</div>
                </div>
            </div>
            <div class="glass-card chart full-width">
                <h3><i class="fas fa-coins"></i> Agent Wealth Comparison</h3>
                <div class="chart-container">
                    <div class="simple-chart" id="wealthChart"></div>
                    <div class="chart-stats" id="wealthStats">$0.00 avg</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateInterval;
        let lastData = null;
        let isRunning = false;

        function updateCharts() {
            Promise.all([
                fetch('/api/data').then(response => response.json()),
                fetch('/api/status').then(response => response.json())
            ]).then(([data, status]) => {
                // Update status with smooth transitions
                updateStatus(status);

                // Only update charts if data has changed (performance optimization)
                if (!lastData || JSON.stringify(data) !== JSON.stringify(lastData)) {
                    updateAgentGrid(data);
                    updatePriceChart(data);
                    updateVolumeChart(data);
                    updateWealthChart(data);
                    updateStats(data);

                    lastData = data;
                }
            }).catch(error => {
                console.error('Error updating charts:', error);
            });
        }

        function updateStatus(status) {
            const statusElement = document.getElementById('status-text');
            const indicatorElement = document.querySelector('.status-indicator');

            // Update indicator with smooth transition
            if (status.running !== isRunning) {
                indicatorElement.classList.remove('status-running', 'status-stopped');
                indicatorElement.classList.add(status.running ? 'status-running' : 'status-stopped');
                isRunning = status.running;
            }

            // Animate status text changes
            const newText = `Status: ${status.running ? 'Running' : 'Stopped'} | ` +
                           `Step: ${status.step} | Agents: ${status.agents} | ` +
                           `Price: $${status.price.toFixed(2)} | Volume: ${status.volume}`;

            if (statusElement.textContent !== newText) {
                statusElement.style.opacity = '0.7';
                setTimeout(() => {
                    statusElement.textContent = newText;
                    statusElement.style.opacity = '1';
                }, 150);
            }
        }

        function updateStats(data) {
            // Update chart statistics
            document.getElementById('gridStats').textContent = `Agents: ${data.agents ? data.agents.length : 0}`;
            document.getElementById('priceStats').textContent = data.prices && data.prices.length > 0 ?
                `$${data.prices[data.prices.length - 1].toFixed(2)}` : '$0.00';
            document.getElementById('volumeStats').textContent = data.volumes && data.volumes.length > 0 ?
                `${data.volumes[data.volumes.length - 1]} transactions` : '0 transactions';
            document.getElementById('wealthStats').textContent = data.wealth_basic && data.wealth_basic.length > 0 ?
                `$${data.wealth_basic[data.wealth_basic.length - 1].toFixed(2)} avg` : '$0.00 avg';
        }

        function updateAgentGrid(data) {
            const grid = document.getElementById('agentGrid');
            grid.innerHTML = '';

            if (!data.agents) return;

            const gridWidth = grid.offsetWidth;
            const gridHeight = grid.offsetHeight;

            data.agents.forEach(agent => {
                const agentDiv = document.createElement('div');
                agentDiv.className = `agent ${agent.type}`;

                // Add action-based styling
                if (agent.action === 'buy') agentDiv.classList.add('buying');
                if (agent.action === 'sell') agentDiv.classList.add('selling');

                // Position based on grid coordinates (assuming 15x15 grid)
                const x = (agent.x / 15) * gridWidth;
                const y = (agent.y / 15) * gridHeight;

                // Size based on wealth
                const size = Math.max(8, Math.min(20, agent.wealth / 80));

                agentDiv.style.left = x + 'px';
                agentDiv.style.top = y + 'px';
                agentDiv.style.width = size + 'px';
                agentDiv.style.height = size + 'px';
                agentDiv.title = `Agent ${agent.id}: $${agent.wealth.toFixed(0)} (${agent.inventory} items) - ${agent.action.toUpperCase()}`;

                grid.appendChild(agentDiv);
            });
        }

        function updatePriceChart(data) {
            const chart = document.getElementById('priceChart');
            chart.innerHTML = '';

            if (!data.prices || data.prices.length < 2) return;

            const maxPrice = Math.max(...data.prices);
            const minPrice = Math.min(...data.prices);
            const priceRange = maxPrice - minPrice || 1;

            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;

            data.prices.forEach((price, index) => {
                if (index === 0) return;

                const x = (index / (data.prices.length - 1)) * chartWidth;
                const y = chartHeight - ((price - minPrice) / priceRange) * chartHeight;

                const point = document.createElement('div');
                point.className = 'line-point';
                point.style.left = x + 'px';
                point.style.top = y + 'px';
                point.title = `Step ${index}: $${price.toFixed(2)}`;

                chart.appendChild(point);
            });
        }

        function updateVolumeChart(data) {
            const chart = document.getElementById('volumeChart');
            chart.innerHTML = '';

            if (!data.volumes || data.volumes.length === 0) return;

            const maxVolume = Math.max(...data.volumes) || 1;
            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;

            const recentVolumes = data.volumes.slice(-10); // Show last 10 steps

            recentVolumes.forEach((volume, index) => {
                const barWidth = chartWidth / recentVolumes.length;
                const barHeight = (volume / maxVolume) * chartHeight;

                const bar = document.createElement('div');
                bar.className = 'bar';
                bar.style.left = (index * barWidth) + 'px';
                bar.style.width = (barWidth - 2) + 'px';
                bar.style.height = barHeight + 'px';
                bar.title = `Volume: ${volume}`;

                chart.appendChild(bar);
            });
        }

        function updateWealthChart(data) {
            const chart = document.getElementById('wealthChart');
            chart.innerHTML = '';

            if (!data.wealth_basic || data.wealth_basic.length < 2) return;

            const maxWealth = Math.max(...data.wealth_basic);
            const minWealth = Math.min(...data.wealth_basic);
            const wealthRange = maxWealth - minWealth || 1;

            const chartWidth = chart.offsetWidth;
            const chartHeight = chart.offsetHeight;

            // Draw basic agents wealth line
            data.wealth_basic.forEach((wealth, index) => {
                if (index === 0) return;

                const x = (index / (data.wealth_basic.length - 1)) * chartWidth;
                const y = chartHeight - ((wealth - minWealth) / wealthRange) * chartHeight;

                const point = document.createElement('div');
                point.className = 'line-point';
                point.style.left = x + 'px';
                point.style.top = y + 'px';
                point.style.background = '#3b82f6';
                point.title = `Step ${index}: $${wealth.toFixed(2)} (Basic)`;

                chart.appendChild(point);
            });

            // Draw smart agents wealth line if available
            if (data.wealth_smart && data.wealth_smart.length > 1 && data.wealth_smart.some(w => w > 0)) {
                data.wealth_smart.forEach((wealth, index) => {
                    if (index === 0 || wealth === 0) return;

                    const x = (index / (data.wealth_smart.length - 1)) * chartWidth;
                    const y = chartHeight - ((wealth - minWealth) / wealthRange) * chartHeight;

                    const point = document.createElement('div');
                    point.className = 'line-point';
                    point.style.left = x + 'px';
                    point.style.top = y + 'px';
                    point.style.background = '#ef4444';
                    point.title = `Step ${index}: $${wealth.toFixed(2)} (Smart)`;

                    chart.appendChild(point);
                });
            }
        }

        function startSimulation() {
            const startBtn = document.querySelector('.btn-start');
            startBtn.style.opacity = '0.7';

            fetch('/api/start')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        updateInterval = setInterval(updateCharts, 200); // Higher frequency for smoother updates
                        startBtn.innerHTML = '<i class="fas fa-play"></i> Running...';
                    }
                    startBtn.style.opacity = '1';
                })
                .catch(error => {
                    console.error('Error starting simulation:', error);
                    startBtn.style.opacity = '1';
                });
        }

        function stopSimulation() {
            const stopBtn = document.querySelector('.btn-stop');
            stopBtn.style.opacity = '0.7';

            fetch('/api/stop')
                .then(response => response.json())
                .then(data => {
                    if (updateInterval) {
                        clearInterval(updateInterval);
                        updateInterval = null;
                    }
                    document.querySelector('.btn-start').innerHTML = '<i class="fas fa-play"></i> Start';
                    stopBtn.style.opacity = '1';
                })
                .catch(error => {
                    console.error('Error stopping simulation:', error);
                    stopBtn.style.opacity = '1';
                });
        }

        function stepSimulation() {
            const stepBtn = document.querySelector('.btn-step');
            stepBtn.style.opacity = '0.7';

            fetch('/api/step')
                .then(response => response.json())
                .then(data => {
                    updateCharts();
                    stepBtn.style.opacity = '1';

                    // Add visual feedback
                    stepBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        stepBtn.style.transform = 'scale(1)';
                    }, 100);
                })
                .catch(error => {
                    console.error('Error stepping simulation:', error);
                    stepBtn.style.opacity = '1';
                });
        }

        function resetSimulation() {
            const resetBtn = document.querySelector('.btn-reset');
            resetBtn.style.opacity = '0.7';
            resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';

            // Stop current simulation
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }

            // Get parameters with validation
            const params = {
                num_basic_agents: Math.max(1, Math.min(50, parseInt(document.getElementById('num_basic_agents').value) || 20)),
                num_smart_agents: Math.max(0, Math.min(20, parseInt(document.getElementById('num_smart_agents').value) || 0)),
                width: Math.max(10, Math.min(30, parseInt(document.getElementById('width').value) || 15)),
                height: Math.max(10, Math.min(30, parseInt(document.getElementById('height').value) || 15)),
                initial_price: Math.max(50, Math.min(200, parseFloat(document.getElementById('initial_price').value) || 100)),
                initial_wealth: Math.max(500, Math.min(2000, parseFloat(document.getElementById('initial_wealth').value) || 1000)),
                initial_inventory: 10
            };

            // Reset with new parameters
            fetch('/api/reset', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(params)
            })
            .then(response => response.json())
            .then(data => {
                // Clear previous data
                lastData = null;

                // Update charts
                updateCharts();

                // Reset UI
                document.querySelector('.btn-start').innerHTML = '<i class="fas fa-play"></i> Start';
                resetBtn.innerHTML = '<i class="fas fa-redo"></i> Reset';
                resetBtn.style.opacity = '1';

                // Add success animation
                resetBtn.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    resetBtn.style.transform = 'scale(1)';
                }, 200);
            })
            .catch(error => {
                console.error('Error resetting simulation:', error);
                resetBtn.innerHTML = '<i class="fas fa-redo"></i> Reset';
                resetBtn.style.opacity = '1';
            });
        }

        // Enhanced initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing charts...');

            // Initialize immediately
            resetSimulation();

            // Set up periodic updates when not running
            setInterval(() => {
                if (!updateInterval) {
                    updateCharts();
                }
            }, 1000);

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            startSimulation();
                            break;
                        case 'p':
                            e.preventDefault();
                            stopSimulation();
                            break;
                        case 'n':
                            e.preventDefault();
                            stepSimulation();
                            break;
                        case 'r':
                            e.preventDefault();
                            resetSimulation();
                            break;
                    }
                }
            });

            // Add parameter change listeners for real-time validation
            document.querySelectorAll('input[type="number"]').forEach(input => {
                input.addEventListener('input', function() {
                    const value = parseFloat(this.value);
                    const min = parseFloat(this.min);
                    const max = parseFloat(this.max);

                    if (value < min || value > max) {
                        this.style.borderColor = '#ef4444';
                    } else {
                        this.style.borderColor = '#e5e7eb';
                    }
                });
            });
        });
    </script>
</body>
</html>