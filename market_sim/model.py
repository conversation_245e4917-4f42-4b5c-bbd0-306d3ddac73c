"""
Core market model for the agent-based simulation.

This module implements the main market dynamics including price mechanisms,
transaction processing, and data collection.
"""

import random
from typing import List, Dict, Any, Tuple
import numpy as np
from mesa import Model
from mesa.space import MultiGrid
from mesa.datacollection import DataCollector


class MarketModel(Model):
    """
    Main market model that manages agents, price dynamics, and transactions.

    The market operates with a simple supply/demand price mechanism where:
    - Prices rise when buy orders exceed sell orders
    - Prices fall when sell orders exceed buy orders
    - Transaction volume is determined by matching orders
    """

    def __init__(
        self,
        num_basic_agents: int = 10,
        num_smart_agents: int = 5,
        width: int = 20,
        height: int = 20,
        initial_price: float = 100.0,
        price_volatility: float = 0.1,
        initial_wealth: float = 1000.0,
        initial_inventory: int = 10
    ):
        """
        Initialize the market model.

        Args:
            num_basic_agents: Number of rule-based agents
            num_smart_agents: Number of AI-powered agents
            width: Grid width
            height: Grid height
            initial_price: Starting market price
            price_volatility: Price change sensitivity
            initial_wealth: Starting cash for each agent
            initial_inventory: Starting inventory for each agent
        """
        super().__init__()

        # Model parameters
        self.num_basic_agents = num_basic_agents
        self.num_smart_agents = num_smart_agents
        self.width = width
        self.height = height
        self.initial_wealth = initial_wealth
        self.initial_inventory = initial_inventory

        # Market state
        self.current_price = initial_price
        self.price_volatility = price_volatility
        self.price_history = [initial_price]

        # Transaction tracking
        self.buy_orders = []
        self.sell_orders = []
        self.transaction_volume = 0
        self.transaction_history = []

        # Grid for spatial representation
        self.grid = MultiGrid(width, height, True)
        # Mesa 3.x visualization expects 'space' attribute
        self.space = self.grid

        # Data collection
        self.datacollector = DataCollector(
            model_reporters={
                "Price": "current_price",
                "Transaction_Volume": "transaction_volume",
                "Total_Wealth_Basic": lambda m: m.get_total_wealth_by_type("basic"),
                "Total_Wealth_Smart": lambda m: m.get_total_wealth_by_type("smart"),
                "Avg_Wealth_Basic": lambda m: m.get_avg_wealth_by_type("basic"),
                "Avg_Wealth_Smart": lambda m: m.get_avg_wealth_by_type("smart"),
                "Buy_Orders": lambda m: len(m.buy_orders),
                "Sell_Orders": lambda m: len(m.sell_orders),
            }
        )

        # Create agents (will be implemented after agent classes are created)
        self.create_agents()

        # Collect initial data
        self.datacollector.collect(self)

    def create_agents(self):
        """Create and place agents on the grid."""
        from .agents.basic_agent import BasicAgent
        from .agents.smart_agent import SmartAgent

        # Create basic agents
        for i in range(self.num_basic_agents):
            agent = BasicAgent(
                model=self,
                wealth=self.initial_wealth,
                inventory=self.initial_inventory,
                belief_volatility=random.uniform(0.02, 0.08),  # Vary agent personalities
                risk_tolerance=random.uniform(0.05, 0.15)
            )
            # Agent is automatically added to model.agents in Mesa 3.x

        # Create smart agents
        for i in range(self.num_smart_agents):
            agent = SmartAgent(
                model=self,
                wealth=self.initial_wealth,
                inventory=self.initial_inventory,
                belief_volatility=random.uniform(0.02, 0.08),  # Vary agent personalities
                risk_tolerance=random.uniform(0.05, 0.15),
                api_timeout=5.0,
                max_retries=2
            )
            # Agent is automatically added to model.agents in Mesa 3.x

        print(f"Created {len(self.agents)} agents ({self.num_basic_agents} basic, {self.num_smart_agents} smart)")

    def step(self):
        """Execute one step of the model."""
        # Clear previous orders
        self.buy_orders = []
        self.sell_orders = []
        self.transaction_volume = 0

        # Activate all agents (Mesa 3.x style)
        for agent in self.agents:
            agent.step()

        # Process market transactions
        self.process_transactions()

        # Update price based on supply/demand
        self.update_price()

        # Record data
        self.datacollector.collect(self)

    def add_buy_order(self, agent_id: int, quantity: int, max_price: float = None):
        """
        Add a buy order to the market.

        Args:
            agent_id: ID of the agent placing the order
            quantity: Number of units to buy
            max_price: Maximum price willing to pay (None for market order)
        """
        if max_price is None:
            max_price = self.current_price * 1.1  # 10% above current price

        self.buy_orders.append({
            'agent_id': agent_id,
            'quantity': quantity,
            'max_price': max_price,
            'type': 'buy'
        })

    def add_sell_order(self, agent_id: int, quantity: int, min_price: float = None):
        """
        Add a sell order to the market.

        Args:
            agent_id: ID of the agent placing the order
            quantity: Number of units to sell
            min_price: Minimum price willing to accept (None for market order)
        """
        if min_price is None:
            min_price = self.current_price * 0.9  # 10% below current price

        self.sell_orders.append({
            'agent_id': agent_id,
            'quantity': quantity,
            'min_price': min_price,
            'type': 'sell'
        })

    def process_transactions(self):
        """
        Match buy and sell orders and execute transactions.

        Uses a simple matching algorithm:
        1. Sort buy orders by price (highest first)
        2. Sort sell orders by price (lowest first)
        3. Match orders where buy price >= sell price
        """
        if not self.buy_orders or not self.sell_orders:
            return

        # Sort orders for optimal matching
        buy_orders = sorted(self.buy_orders, key=lambda x: x['max_price'], reverse=True)
        sell_orders = sorted(self.sell_orders, key=lambda x: x['min_price'])

        transactions = []

        for buy_order in buy_orders:
            for sell_order in sell_orders:
                # Check if transaction is possible
                if buy_order['max_price'] >= sell_order['min_price']:
                    # Calculate transaction details
                    quantity = min(buy_order['quantity'], sell_order['quantity'])
                    price = (buy_order['max_price'] + sell_order['min_price']) / 2

                    # Record transaction
                    transaction = {
                        'buyer_id': buy_order['agent_id'],
                        'seller_id': sell_order['agent_id'],
                        'quantity': quantity,
                        'price': price,
                        'step': self.steps
                    }
                    transactions.append(transaction)

                    # Update order quantities
                    buy_order['quantity'] -= quantity
                    sell_order['quantity'] -= quantity

                    # Update transaction volume
                    self.transaction_volume += quantity

                    # Remove fulfilled orders
                    if buy_order['quantity'] <= 0:
                        break
                    if sell_order['quantity'] <= 0:
                        continue

        # Execute transactions (update agent wealth and inventory)
        for transaction in transactions:
            self.execute_transaction(transaction)

        # Store transaction history
        self.transaction_history.extend(transactions)

    def execute_transaction(self, transaction: Dict[str, Any]):
        """
        Execute a transaction by updating agent wealth and inventory.

        Args:
            transaction: Transaction details dictionary
        """
        # Find agents by ID
        buyer = None
        seller = None
        for agent in self.agents:
            if agent.unique_id == transaction['buyer_id']:
                buyer = agent
            elif agent.unique_id == transaction['seller_id']:
                seller = agent

        if buyer is None or seller is None:
            return  # Skip if agents not found

        total_cost = transaction['quantity'] * transaction['price']

        # Update buyer
        buyer.wealth -= total_cost
        buyer.inventory += transaction['quantity']

        # Update seller
        seller.wealth += total_cost
        seller.inventory -= transaction['quantity']

    def update_price(self):
        """
        Update market price based on supply and demand.

        Price increases when buy orders exceed sell orders,
        decreases when sell orders exceed buy orders.
        """
        total_buy_quantity = sum(order['quantity'] for order in self.buy_orders)
        total_sell_quantity = sum(order['quantity'] for order in self.sell_orders)

        # Calculate supply/demand imbalance
        if total_buy_quantity + total_sell_quantity > 0:
            demand_ratio = total_buy_quantity / (total_buy_quantity + total_sell_quantity)
            supply_ratio = total_sell_quantity / (total_buy_quantity + total_sell_quantity)

            # Price change based on imbalance
            price_change = (demand_ratio - supply_ratio) * self.price_volatility * self.current_price

            # Add some random noise
            noise = random.gauss(0, self.current_price * 0.01)

            # Update price
            self.current_price = max(1.0, self.current_price + price_change + noise)

        # Record price history
        self.price_history.append(self.current_price)

    def get_total_wealth_by_type(self, agent_type: str) -> float:
        """Get total wealth for agents of a specific type."""
        total = 0
        for agent in self.agents:
            if hasattr(agent, 'agent_type') and agent.agent_type == agent_type:
                total += agent.wealth + (agent.inventory * self.current_price)
        return total

    def get_avg_wealth_by_type(self, agent_type: str) -> float:
        """Get average wealth for agents of a specific type."""
        agents_of_type = [
            agent for agent in self.agents
            if hasattr(agent, 'agent_type') and agent.agent_type == agent_type
        ]
        if not agents_of_type:
            return 0

        total_wealth = sum(
            agent.wealth + (agent.inventory * self.current_price)
            for agent in agents_of_type
        )
        return total_wealth / len(agents_of_type)

    def get_market_context(self) -> Dict[str, Any]:
        """
        Get current market context for AI agents.

        Returns:
            Dictionary containing market state information
        """
        recent_prices = self.price_history[-10:] if len(self.price_history) >= 10 else self.price_history

        return {
            'current_price': self.current_price,
            'price_history': recent_prices,
            'price_trend': self.calculate_price_trend(),
            'transaction_volume': self.transaction_volume,
            'buy_orders_count': len(self.buy_orders),
            'sell_orders_count': len(self.sell_orders),
            'market_activity': len(self.transaction_history[-5:]) if self.transaction_history else 0,
            'step': self.steps
        }

    def calculate_price_trend(self) -> str:
        """Calculate recent price trend."""
        if len(self.price_history) < 3:
            return "stable"

        recent_prices = self.price_history[-3:]
        if recent_prices[-1] > recent_prices[-2] > recent_prices[-3]:
            return "rising"
        elif recent_prices[-1] < recent_prices[-2] < recent_prices[-3]:
            return "falling"
        else:
            return "stable"
