"""
AI-powered smart agent for the market simulation.

This agent uses Google's Gemini API to make sophisticated trading decisions based on:
- Current market conditions and trends
- Personal financial state and risk profile
- Market sentiment and activity levels
- Advanced reasoning about market dynamics
"""

import os
import json
import random
import time
import numpy as np
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types

from .basic_agent import BasicAgent

# Load environment variables
load_dotenv()


class SmartAgent(BasicAgent):
    """
    An AI-powered agent that uses Gemini API for sophisticated trading decisions.

    Inherits from BasicAgent to maintain compatibility and provide fallback behavior
    when API calls fail.
    """

    def __init__(
        self,
        model,
        wealth: float = 1000.0,
        inventory: int = 10,
        belief_volatility: float = 0.05,
        risk_tolerance: float = 0.1,
        api_timeout: float = 5.0,
        max_retries: int = 2
    ):
        """
        Initialize a smart agent with AI capabilities.

        Args:
            model: The market model instance
            wealth: Starting cash amount
            inventory: Starting inventory of assets
            belief_volatility: How much the agent's belief can change per step
            risk_tolerance: How much risk the agent is willing to take (0-1)
            api_timeout: Timeout for API calls in seconds
            max_retries: Maximum number of API retry attempts
        """
        super().__init__(model, wealth, inventory, belief_volatility, risk_tolerance)

        # Agent type identifier
        self.agent_type = "smart"

        # AI-specific parameters
        self.api_timeout = api_timeout
        self.max_retries = max_retries

        # Performance tracking
        self.api_calls_made = 0
        self.api_failures = 0
        self.fallback_decisions = 0
        self.ai_decisions = 0
        self.decision_confidence_history = []
        self.reasoning_history = []

        # Initialize Gemini API
        self.setup_gemini_api()

        # AI decision cache (for debugging and analysis)
        self.last_ai_response = None
        self.last_reasoning = ""
        self.last_confidence = 0.0

    def setup_gemini_api(self):
        """Initialize the Gemini API client."""
        try:
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                raise ValueError("GEMINI_API_KEY not found in environment")

            self.genai_client = genai.Client(api_key=api_key)
            self.model_name = "gemini-2.0-flash"
            self.api_available = True

        except Exception as e:
            print(f"⚠️  SmartAgent {self.unique_id}: Failed to initialize Gemini API: {e}")
            self.api_available = False
            self.genai_client = None

    def step(self):
        """Execute one step of smart agent behavior."""
        # Update belief about fair value (inherited from BasicAgent)
        self.update_belief()

        # Make AI-powered trading decision
        action = self.make_smart_decision()

        # Execute the action
        self.execute_action(action)

        # Record action
        self.last_action = action
        self.actions_history.append(action)

        # Move on grid (simple random walk)
        self.move()

    def make_smart_decision(self) -> str:
        """
        Make a trading decision using AI analysis.

        Returns:
            str: "buy", "sell", or "hold"
        """
        if not self.api_available:
            # Fallback to basic agent logic
            self.fallback_decisions += 1
            return super().make_decision()

        try:
            # Prepare market context for AI
            context = self.prepare_market_context()

            # Get AI decision
            ai_response = self.call_gemini_api(context)

            if ai_response:
                # Parse AI response
                decision, confidence, reasoning = self.parse_ai_response(ai_response)

                # Record AI decision metrics
                self.ai_decisions += 1
                self.decision_confidence_history.append(confidence)
                self.reasoning_history.append(reasoning)
                self.last_reasoning = reasoning
                self.last_confidence = confidence

                return decision
            else:
                # API call failed, use fallback
                self.fallback_decisions += 1
                return super().make_decision()

        except Exception as e:
            print(f"⚠️  SmartAgent {self.unique_id}: AI decision failed: {e}")
            self.api_failures += 1
            self.fallback_decisions += 1
            return super().make_decision()

    def prepare_market_context(self) -> Dict[str, Any]:
        """
        Prepare comprehensive market context for AI analysis.

        Returns:
            Dict containing all relevant market and agent information
        """
        # Get market trend
        trend = self.get_price_trend()

        # Calculate market activity level
        recent_volume = sum(self.model.transaction_history[-5:]) if len(self.model.transaction_history) >= 5 else 0
        activity_level = "high" if recent_volume > 10 else "medium" if recent_volume > 5 else "low"

        # Calculate price volatility
        if len(self.model.price_history) >= 10:
            recent_prices = self.model.price_history[-10:]
            price_volatility = np.std(recent_prices) / np.mean(recent_prices) if recent_prices else 0
        else:
            price_volatility = 0.05  # Default assumption

        # Calculate relative performance
        performance = self.get_performance()

        context = {
            # Agent state
            "current_wealth": round(self.wealth, 2),
            "current_inventory": self.inventory,
            "fair_value_belief": round(self.fair_value_belief, 2),
            "risk_tolerance": self.risk_tolerance,
            "total_value": round(self.get_total_value(), 2),
            "performance": round(performance * 100, 1),  # As percentage

            # Market state
            "current_price": round(self.model.current_price, 2),
            "price_trend": trend,
            "market_activity": activity_level,
            "price_volatility": round(price_volatility, 3),
            "transaction_volume": self.model.transaction_volume,

            # Historical context
            "steps_elapsed": len(self.model.price_history),
            "recent_actions": self.actions_history[-5:] if len(self.actions_history) >= 5 else self.actions_history,

            # Market comparison
            "price_vs_belief_ratio": round(self.model.current_price / self.fair_value_belief, 3),
            "can_afford_purchase": self.can_afford_purchase(),
            "has_inventory": self.inventory > 0
        }

        return context

    def call_gemini_api(self, context: Dict[str, Any]) -> Optional[str]:
        """
        Call Gemini API with market context to get trading decision.

        Args:
            context: Market and agent context dictionary

        Returns:
            Raw AI response string or None if failed
        """
        user_prompt = self.create_concise_prompt(context)
        system_instruction = self.get_system_instruction()

        for attempt in range(self.max_retries + 1):
            try:
                self.api_calls_made += 1

                contents = [
                    types.Content(
                        role="user",
                        parts=[types.Part.from_text(text=user_prompt)]
                    )
                ]

                config = types.GenerateContentConfig(
                    response_mime_type="text/plain",
                    system_instruction=[
                        types.Part.from_text(text=system_instruction)
                    ],
                    temperature=0.7,
                    max_output_tokens=300,
                )

                response = self.genai_client.models.generate_content(
                    model=self.model_name,
                    contents=contents,
                    config=config
                )

                if response and response.text:
                    self.last_ai_response = response.text
                    return response.text

            except Exception as e:
                if attempt < self.max_retries:
                    time.sleep(0.5 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    print(f"⚠️  SmartAgent {self.unique_id}: API call failed after {self.max_retries + 1} attempts: {e}")
                    self.api_failures += 1
                    return None

        return None

    def get_system_instruction(self) -> str:
        """
        Get the system instruction for the AI trader.

        Returns:
            System instruction string
        """
        return """You are a sophisticated AI trader in a market simulation. Your goal is to maximize your portfolio value through intelligent trading decisions.

TRADING RULES:
- You can BUY when you have sufficient wealth
- You can SELL when you have inventory
- You can HOLD to wait for better opportunities

DECISION FACTORS:
1. Value Assessment: Compare current price to your fair value belief
2. Trend Analysis: Consider price trends (rising/falling/stable)
3. Risk Management: Respect your risk tolerance level
4. Portfolio Balance: Maintain appropriate wealth/inventory mix
5. Market Timing: Consider market activity levels

RESPONSE FORMAT:
Always respond with valid JSON with no extra comments:
{
    "decision": "buy" | "sell" | "hold",
    "confidence": 0.0-1.0,
    "reasoning": "Brief explanation"
}

Be decisive, analytical, and profit-focused."""

    def create_concise_prompt(self, context: Dict[str, Any]) -> str:
        """
        Create a concise prompt with just the essential market data.

        Args:
            context: Market and agent context

        Returns:
            Concise prompt string
        """
        return f"""MARKET DATA:
Price: ${context['current_price']} | Trend: {context['price_trend']} | Activity: {context['market_activity']}
Your Belief: ${context['fair_value_belief']} | Ratio: {context['price_vs_belief_ratio']:.3f}

PORTFOLIO:
Wealth: ${context['current_wealth']} | Inventory: {context['current_inventory']} units
Total Value: ${context['total_value']} | Performance: {context['performance']:+.1f}%
Risk Tolerance: {context['risk_tolerance']*100:.1f}%

CONSTRAINTS:
Can Buy: {context['can_afford_purchase']} | Can Sell: {context['has_inventory']}
Recent Actions: {context['recent_actions'][-3:] if len(context['recent_actions']) >= 3 else context['recent_actions']}

Make your trading decision:"""

    def create_trading_prompt(self, context: Dict[str, Any]) -> str:
        """
        Create a comprehensive prompt for the AI trading decision.

        Args:
            context: Market and agent context

        Returns:
            Formatted prompt string
        """
        prompt = f"""You are a sophisticated AI trader operating in a dynamic market simulation. Analyze the current situation and make an optimal trading decision.

CURRENT SITUATION:
• Your Wealth: ${context['current_wealth']}
• Your Inventory: {context['current_inventory']} units
• Your Fair Value Belief: ${context['fair_value_belief']}
• Your Risk Tolerance: {context['risk_tolerance']*100:.1f}%
• Your Total Portfolio Value: ${context['total_value']}
• Your Performance: {context['performance']:+.1f}%

MARKET CONDITIONS:
• Current Market Price: ${context['current_price']}
• Price Trend: {context['price_trend']}
• Market Activity Level: {context['market_activity']}
• Price Volatility: {context['price_volatility']:.1f}%
• Recent Transaction Volume: {context['transaction_volume']} units
• Market Steps Elapsed: {context['steps_elapsed']}

ANALYSIS FACTORS:
• Price vs Your Belief Ratio: {context['price_vs_belief_ratio']:.3f}
• Can Afford Purchase: {context['can_afford_purchase']}
• Have Inventory to Sell: {context['has_inventory']}
• Your Recent Actions: {context['recent_actions']}

DECISION FRAMEWORK:
Consider these factors in your analysis:
1. Value Assessment: Is the current price above or below your fair value belief?
2. Trend Analysis: How might the {context['price_trend']} trend affect future prices?
3. Risk Management: Given your {context['risk_tolerance']*100:.1f}% risk tolerance, what's appropriate?
4. Portfolio Balance: How does this decision fit your current wealth/inventory mix?
5. Market Timing: Is this a good time to act given {context['market_activity']} activity?

REQUIRED RESPONSE FORMAT:
Respond with exactly this JSON structure:
{{
    "decision": "buy" | "sell" | "hold",
    "confidence": 0.0-1.0,
    "reasoning": "Brief explanation of your decision logic"
}}

Make your decision now:"""

        return prompt

    def parse_ai_response(self, response: str) -> tuple[str, float, str]:
        """
        Parse AI response to extract decision, confidence, and reasoning.

        Args:
            response: Raw AI response string

        Returns:
            Tuple of (decision, confidence, reasoning)
        """
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)

            if json_match:
                json_str = json_match.group()
                parsed = json.loads(json_str)

                decision = parsed.get('decision', 'hold').lower()
                confidence = float(parsed.get('confidence', 0.5))
                reasoning = parsed.get('reasoning', 'No reasoning provided')

                # Validate decision
                if decision not in ['buy', 'sell', 'hold']:
                    decision = 'hold'

                # Clamp confidence to valid range
                confidence = max(0.0, min(1.0, confidence))

                return decision, confidence, reasoning

        except Exception as e:
            print(f"⚠️  SmartAgent {self.unique_id}: Failed to parse AI response: {e}")

        # Fallback parsing - look for keywords
        response_lower = response.lower()
        if 'buy' in response_lower:
            return 'buy', 0.5, 'Keyword-based parsing'
        elif 'sell' in response_lower:
            return 'sell', 0.5, 'Keyword-based parsing'
        else:
            return 'hold', 0.5, 'Default fallback decision'

    def get_ai_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics for the AI agent.

        Returns:
            Dictionary of performance metrics
        """
        total_decisions = self.ai_decisions + self.fallback_decisions

        metrics = {
            # API usage metrics
            "api_calls_made": self.api_calls_made,
            "api_failures": self.api_failures,
            "api_success_rate": (self.api_calls_made - self.api_failures) / max(1, self.api_calls_made),

            # Decision metrics
            "ai_decisions": self.ai_decisions,
            "fallback_decisions": self.fallback_decisions,
            "ai_decision_rate": self.ai_decisions / max(1, total_decisions),

            # Confidence metrics
            "avg_confidence": sum(self.decision_confidence_history) / max(1, len(self.decision_confidence_history)),
            "confidence_history": self.decision_confidence_history[-10:],  # Last 10 decisions

            # Performance metrics
            "financial_performance": self.get_performance(),
            "total_value": self.get_total_value(),
            "wealth": self.wealth,
            "inventory": self.inventory,

            # Recent activity
            "last_reasoning": self.last_reasoning,
            "last_confidence": self.last_confidence,
            "recent_actions": self.actions_history[-5:] if len(self.actions_history) >= 5 else self.actions_history
        }

        return metrics
