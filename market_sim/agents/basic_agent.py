"""
Basic rule-based agent for the market simulation.

This agent uses simple rules to make buy/sell decisions based on:
- Current price vs. personal belief about fair value
- Recent price trends
- Current wealth and inventory levels
"""

import random
from typing import Optional
from mesa import Agent


class BasicAgent(Agent):
    """
    A rule-based agent that makes simple trading decisions.
    
    The agent maintains a belief about the fair value of the asset and
    makes decisions based on comparing current price to this belief.
    """
    
    def __init__(
        self,
        model,
        wealth: float = 1000.0,
        inventory: int = 10,
        belief_volatility: float = 0.05,
        risk_tolerance: float = 0.1
    ):
        """
        Initialize a basic agent.
        
        Args:
            model: The market model instance
            wealth: Starting cash amount
            inventory: Starting inventory of assets
            belief_volatility: How much the agent's belief can change per step
            risk_tolerance: How much risk the agent is willing to take (0-1)
        """
        super().__init__(model)
        
        # Agent type identifier
        self.agent_type = "basic"
        
        # Financial state
        self.wealth = wealth
        self.inventory = inventory
        self.initial_wealth = wealth
        self.initial_inventory = inventory
        
        # Trading parameters
        self.belief_volatility = belief_volatility
        self.risk_tolerance = risk_tolerance
        
        # Agent's belief about fair value (starts near current market price)
        self.fair_value_belief = model.current_price * random.uniform(0.9, 1.1)
        
        # Trading history
        self.last_action = "hold"
        self.actions_history = []
        
        # Place agent on grid
        self.place_on_grid()
    
    def place_on_grid(self):
        """Place the agent randomly on the grid."""
        x = self.model.random.randrange(self.model.grid.width)
        y = self.model.random.randrange(self.model.grid.height)
        self.model.grid.place_agent(self, (x, y))
    
    def step(self):
        """Execute one step of agent behavior."""
        # Update belief about fair value
        self.update_belief()
        
        # Make trading decision
        action = self.make_decision()
        
        # Execute the action
        self.execute_action(action)
        
        # Record action
        self.last_action = action
        self.actions_history.append(action)
        
        # Move on grid (simple random walk)
        self.move()
    
    def update_belief(self):
        """
        Update the agent's belief about fair value.
        
        The agent slowly adjusts their belief based on:
        - Recent price movements
        - Random noise (representing new information)
        """
        current_price = self.model.current_price
        
        # Adjust belief slightly toward current price (price discovery)
        price_adjustment = (current_price - self.fair_value_belief) * 0.1
        
        # Add some random noise
        noise = random.gauss(0, self.fair_value_belief * self.belief_volatility)
        
        # Update belief
        self.fair_value_belief += price_adjustment + noise
        
        # Keep belief positive
        self.fair_value_belief = max(1.0, self.fair_value_belief)
    
    def make_decision(self) -> str:
        """
        Make a trading decision based on current market conditions.
        
        Returns:
            str: "buy", "sell", or "hold"
        """
        current_price = self.model.current_price
        
        # Calculate how much the agent thinks the asset is mispriced
        price_ratio = current_price / self.fair_value_belief
        
        # Get recent price trend
        trend = self.get_price_trend()
        
        # Decision logic
        if price_ratio < (1 - self.risk_tolerance):
            # Price is below fair value - consider buying
            if self.can_afford_purchase() and (trend != "falling" or random.random() < 0.3):
                return "buy"
        
        elif price_ratio > (1 + self.risk_tolerance):
            # Price is above fair value - consider selling
            if self.inventory > 0 and (trend != "rising" or random.random() < 0.3):
                return "sell"
        
        # Default to holding
        return "hold"
    
    def get_price_trend(self) -> str:
        """
        Analyze recent price trend.
        
        Returns:
            str: "rising", "falling", or "stable"
        """
        price_history = self.model.price_history
        
        if len(price_history) < 3:
            return "stable"
        
        recent_prices = price_history[-3:]
        
        if recent_prices[-1] > recent_prices[-2] > recent_prices[-3]:
            return "rising"
        elif recent_prices[-1] < recent_prices[-2] < recent_prices[-3]:
            return "falling"
        else:
            return "stable"
    
    def can_afford_purchase(self) -> bool:
        """Check if agent can afford to buy at current price."""
        return self.wealth >= self.model.current_price
    
    def execute_action(self, action: str):
        """
        Execute the chosen trading action.
        
        Args:
            action: "buy", "sell", or "hold"
        """
        if action == "buy" and self.can_afford_purchase():
            # Determine quantity to buy (1-3 units, based on wealth)
            max_affordable = int(self.wealth // self.model.current_price)
            quantity = min(random.randint(1, 3), max_affordable)
            
            if quantity > 0:
                self.model.add_buy_order(self.unique_id, quantity)
        
        elif action == "sell" and self.inventory > 0:
            # Determine quantity to sell (1-3 units, based on inventory)
            quantity = min(random.randint(1, 3), self.inventory)
            self.model.add_sell_order(self.unique_id, quantity)
        
        # "hold" requires no action
    
    def move(self):
        """Move agent to a neighboring cell."""
        possible_steps = self.model.grid.get_neighborhood(
            self.pos, moore=True, include_center=False
        )
        new_position = self.model.random.choice(possible_steps)
        self.model.grid.move_agent(self, new_position)
    
    def get_total_value(self) -> float:
        """Calculate total value (wealth + inventory value)."""
        return self.wealth + (self.inventory * self.model.current_price)
    
    def get_performance(self) -> float:
        """Calculate performance relative to initial value."""
        initial_value = self.initial_wealth + (self.initial_inventory * self.model.current_price)
        current_value = self.get_total_value()
        return (current_value - initial_value) / initial_value if initial_value > 0 else 0.0
