"""
Scale testing for the market simulation.

This script tests the system with larger numbers of agents to validate:
1. Market stability with more participants
2. Performance with 20-50 agents
3. Transaction volume and price dynamics
4. System performance and memory usage
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add parent directory to path so we can import market_sim
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from market_sim.model import MarketModel

def test_agent_scaling():
    """Test the system with increasing numbers of agents."""
    print("🔬 Testing Market Simulation Scaling")
    print("=" * 50)
    
    load_dotenv()
    
    # Test different agent counts
    agent_counts = [5, 10, 20, 30, 50]
    
    for num_agents in agent_counts:
        print(f"\n📊 Testing with {num_agents} agents...")
        
        # Create model
        start_time = time.time()
        model = MarketModel(
            num_basic_agents=num_agents,
            num_smart_agents=0,
            width=max(10, int(num_agents**0.5) + 5),  # Scale grid with agents
            height=max(10, int(num_agents**0.5) + 5),
            initial_price=100.0,
            initial_wealth=1000.0,
            initial_inventory=10
        )
        creation_time = time.time() - start_time
        
        print(f"   ✅ Created {len(model.agents)} agents in {creation_time:.3f}s")
        print(f"   📏 Grid size: {model.width}x{model.height}")
        
        # Run simulation
        steps = 20
        start_time = time.time()
        
        total_transactions = 0
        price_changes = []
        
        for step in range(steps):
            step_start = time.time()
            model.step()
            step_time = time.time() - step_start
            
            total_transactions += len([t for t in model.transaction_history if t['step'] == step])
            if len(model.price_history) >= 2:
                price_change = abs(model.price_history[-1] - model.price_history[-2])
                price_changes.append(price_change)
            
            if step == 0:
                print(f"   ⏱️  First step time: {step_time:.3f}s")
        
        simulation_time = time.time() - start_time
        avg_step_time = simulation_time / steps
        
        # Calculate metrics
        final_price = model.current_price
        price_volatility = sum(price_changes) / len(price_changes) if price_changes else 0
        avg_transactions_per_step = total_transactions / steps
        
        # Agent wealth analysis
        total_wealth = sum(agent.get_total_value() for agent in model.agents)
        wealth_std = 0
        if len(model.agents) > 1:
            wealth_values = [agent.get_total_value() for agent in model.agents]
            mean_wealth = sum(wealth_values) / len(wealth_values)
            wealth_std = (sum((w - mean_wealth)**2 for w in wealth_values) / len(wealth_values))**0.5
        
        print(f"   📈 Final price: ${final_price:.2f}")
        print(f"   📊 Total transactions: {total_transactions}")
        print(f"   💰 Total wealth: ${total_wealth:.0f}")
        print(f"   📉 Wealth std dev: ${wealth_std:.0f}")
        print(f"   ⚡ Avg step time: {avg_step_time:.3f}s")
        print(f"   🔄 Transactions/step: {avg_transactions_per_step:.1f}")
        
        # Performance assessment
        if avg_step_time > 1.0:
            print(f"   ⚠️  Performance warning: Steps taking {avg_step_time:.3f}s")
        elif avg_step_time < 0.1:
            print(f"   🚀 Excellent performance: {avg_step_time:.3f}s per step")
        else:
            print(f"   ✅ Good performance: {avg_step_time:.3f}s per step")

def test_market_stability():
    """Test market stability with a larger simulation."""
    print(f"\n🏛️  Testing Market Stability (30 agents, 100 steps)")
    print("=" * 55)
    
    model = MarketModel(
        num_basic_agents=30,
        num_smart_agents=0,
        width=15,
        height=15,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )
    
    print(f"Initial conditions:")
    print(f"   - Agents: {len(model.agents)}")
    print(f"   - Starting price: ${model.current_price:.2f}")
    print(f"   - Total initial wealth: ${sum(agent.get_total_value() for agent in model.agents):.0f}")
    
    # Run longer simulation
    steps = 100
    print(f"\nRunning {steps} steps...")
    
    # Track key metrics
    prices = [model.current_price]
    volumes = []
    wealth_totals = []
    
    for step in range(steps):
        model.step()
        prices.append(model.current_price)
        volumes.append(model.transaction_volume)
        wealth_totals.append(sum(agent.get_total_value() for agent in model.agents))
        
        if (step + 1) % 20 == 0:
            print(f"   Step {step + 1:3d}: Price=${model.current_price:6.2f}, "
                  f"Volume={model.transaction_volume:2d}, "
                  f"Wealth=${wealth_totals[-1]:8.0f}")
    
    # Stability analysis
    print(f"\n📊 Stability Analysis:")
    
    # Price stability
    price_min, price_max = min(prices), max(prices)
    price_range = price_max - price_min
    price_volatility = price_range / prices[0] * 100
    
    print(f"   💰 Price range: ${price_min:.2f} - ${price_max:.2f} ({price_volatility:.1f}% volatility)")
    
    # Volume consistency
    avg_volume = sum(volumes) / len(volumes)
    max_volume = max(volumes)
    print(f"   📊 Volume: avg={avg_volume:.1f}, max={max_volume}")
    
    # Wealth conservation
    initial_wealth = wealth_totals[0]
    final_wealth = wealth_totals[-1]
    wealth_change = (final_wealth - initial_wealth) / initial_wealth * 100
    print(f"   💎 Wealth conservation: {wealth_change:+.2f}% change")
    
    # Market activity
    active_steps = sum(1 for v in volumes if v > 0)
    activity_rate = active_steps / len(volumes) * 100
    print(f"   🔄 Market activity: {activity_rate:.1f}% of steps had transactions")
    
    # Stability assessment
    if price_volatility < 20 and abs(wealth_change) < 5 and activity_rate > 30:
        print(f"   ✅ Market appears STABLE")
    elif price_volatility < 50 and abs(wealth_change) < 10:
        print(f"   ⚠️  Market shows MODERATE stability")
    else:
        print(f"   ❌ Market appears UNSTABLE")
    
    return model

def test_agent_diversity():
    """Test how agent diversity affects market dynamics."""
    print(f"\n🎭 Testing Agent Diversity Effects")
    print("=" * 40)
    
    # Test with different agent personality ranges
    scenarios = [
        ("Conservative", 0.02, 0.08, 0.05, 0.10),  # Low volatility, low risk
        ("Moderate", 0.03, 0.07, 0.08, 0.15),      # Medium settings
        ("Aggressive", 0.05, 0.12, 0.10, 0.25),   # High volatility, high risk
    ]
    
    for scenario_name, min_vol, max_vol, min_risk, max_risk in scenarios:
        print(f"\n{scenario_name} agents:")
        
        # Create model with specific agent types
        model = MarketModel(
            num_basic_agents=20,
            num_smart_agents=0,
            width=12,
            height=12,
            initial_price=100.0
        )
        
        # Manually set agent personalities
        for agent in model.agents:
            agent.belief_volatility = min_vol + (max_vol - min_vol) * model.random.random()
            agent.risk_tolerance = min_risk + (max_risk - min_risk) * model.random.random()
        
        # Run simulation
        for _ in range(30):
            model.step()
        
        # Analyze results
        price_change = (model.current_price - 100.0) / 100.0 * 100
        total_transactions = len(model.transaction_history)
        avg_volume = sum(t['quantity'] for t in model.transaction_history) / len(model.transaction_history) if model.transaction_history else 0
        
        print(f"   Price change: {price_change:+.1f}%")
        print(f"   Total transactions: {total_transactions}")
        print(f"   Avg transaction size: {avg_volume:.1f}")

if __name__ == "__main__":
    test_agent_scaling()
    model = test_market_stability()
    test_agent_diversity()
    
    print(f"\n🎉 Scale testing completed! System ready for visualization implementation.")
