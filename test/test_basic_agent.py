"""
Test script for Basic Agent and Market Model integration.

This script tests:
1. Agent creation and placement
2. Market transactions
3. Price updates
4. Data collection
"""

import os
import sys
from dotenv import load_dotenv

# Add parent directory to path so we can import market_sim
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from market_sim.model import MarketModel

def test_basic_simulation():
    """Run a basic simulation to test the model and agents."""
    print("🧪 Testing Basic Agent and Market Model Integration")
    print("=" * 60)

    # Load environment
    load_dotenv()

    # Create a small model for testing
    model = MarketModel(
        num_basic_agents=5,
        num_smart_agents=0,  # No smart agents yet
        width=10,
        height=10,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )

    print(f"✅ Model created successfully")
    print(f"   - Agents: {len(model.agents)}")
    print(f"   - Initial price: ${model.current_price:.2f}")
    print(f"   - Grid size: {model.width}x{model.height}")
    print()

    # Test agent properties
    print("👥 Agent Details:")
    for i, agent in enumerate(model.agents):
        print(f"   Agent {i+1}: ${agent.wealth:.0f} cash, {agent.inventory} inventory, "
              f"believes fair value is ${agent.fair_value_belief:.2f}")
    print()

    # Run simulation for several steps
    print("🚀 Running simulation...")
    print("Step | Price  | Volume | Buy Orders | Sell Orders | Total Wealth")
    print("-" * 65)

    for step in range(10):
        model.step()

        total_wealth = sum(agent.get_total_value() for agent in model.agents)

        print(f"{step+1:4d} | ${model.current_price:6.2f} | {model.transaction_volume:6d} | "
              f"{len(model.buy_orders):10d} | {len(model.sell_orders):11d} | ${total_wealth:11.0f}")

    print()

    # Final analysis
    print("📊 Final Analysis:")
    print(f"   - Final price: ${model.current_price:.2f}")
    print(f"   - Price change: {((model.current_price / model.price_history[0]) - 1) * 100:+.1f}%")
    print(f"   - Total transactions: {len(model.transaction_history)}")
    print(f"   - Average volume per step: {sum(t['quantity'] for t in model.transaction_history) / len(model.price_history):.1f}")
    print()

    # Agent performance
    print("🏆 Agent Performance:")
    for i, agent in enumerate(model.agents):
        performance = agent.get_performance() * 100
        print(f"   Agent {i+1}: {performance:+.1f}% (${agent.get_total_value():.0f} total value)")

    print()

    # Test data collection
    print("📈 Data Collection Test:")
    data = model.datacollector.get_model_vars_dataframe()
    print(f"   - Collected {len(data)} data points")
    print(f"   - Columns: {list(data.columns)}")
    print(f"   - Price range: ${data['Price'].min():.2f} - ${data['Price'].max():.2f}")

    print()
    print("✅ All tests completed successfully!")
    return model

def test_agent_decision_making():
    """Test individual agent decision-making logic."""
    print("\n🧠 Testing Agent Decision Making Logic")
    print("=" * 40)

    # Create a simple model
    model = MarketModel(num_basic_agents=1, num_smart_agents=0, width=5, height=5)
    agent = model.agents[0]

    print(f"Agent belief: ${agent.fair_value_belief:.2f}")
    print(f"Current price: ${model.current_price:.2f}")
    print(f"Agent wealth: ${agent.wealth:.0f}")
    print(f"Agent inventory: {agent.inventory}")

    # Test decision making in different scenarios
    scenarios = [
        ("Current market", model.current_price),
        ("Undervalued market", agent.fair_value_belief * 0.8),
        ("Overvalued market", agent.fair_value_belief * 1.2),
        ("Very cheap", agent.fair_value_belief * 0.5),
        ("Very expensive", agent.fair_value_belief * 1.5),
    ]

    print("\nDecision testing:")
    for scenario_name, test_price in scenarios:
        model.current_price = test_price
        decision = agent.make_decision()
        print(f"   {scenario_name:18s} (${test_price:6.2f}): {decision}")

    print("✅ Decision making test completed!")

if __name__ == "__main__":
    # Run tests
    model = test_basic_simulation()
    test_agent_decision_making()

    print("\n🎉 All tests passed! The basic agent and market model are working correctly.")
