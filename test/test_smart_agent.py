"""
Test script for Smart Agent with Gemini API integration.

This script tests:
1. Smart agent creation and API setup
2. AI-powered decision making
3. Fallback behavior when API fails
4. Performance metrics collection
5. Integration with market model
"""

import os
import sys
from dotenv import load_dotenv

# Add parent directory to path so we can import market_sim
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from market_sim.model import MarketModel

def test_smart_agent_creation():
    """Test smart agent creation and basic functionality."""
    print("🧪 Testing Smart Agent Creation and Setup")
    print("=" * 60)

    # Load environment
    load_dotenv()

    # Check API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found. Smart agent tests will be limited.")
        return False

    print(f"✅ API key found: {api_key[:10]}...")

    # Create a model with smart agents
    model = MarketModel(
        num_basic_agents=3,
        num_smart_agents=2,  # 10% smart agents (2 out of 5 total)
        width=10,
        height=10,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )

    print(f"✅ Model created with {len(model.agents)} agents")

    # Check agent types
    basic_agents = [a for a in model.agents if a.agent_type == "basic"]
    smart_agents = [a for a in model.agents if a.agent_type == "smart"]

    print(f"   Basic agents: {len(basic_agents)}")
    print(f"   Smart agents: {len(smart_agents)}")

    if len(smart_agents) != 2:
        print(f"❌ Expected 2 smart agents, got {len(smart_agents)}")
        return False

    # Test smart agent API setup
    for agent in smart_agents:
        print(f"   Smart Agent {agent.unique_id}:")
        print(f"     API Available: {agent.api_available}")
        print(f"     Model: {getattr(agent, 'model_name', 'N/A')}")

    print("✅ Smart agent creation test completed!")
    return model

def test_smart_agent_decisions():
    """Test smart agent decision making."""
    print("\n🧪 Testing Smart Agent Decision Making")
    print("=" * 60)

    # Create model
    model = test_smart_agent_creation()
    if not model:
        return False

    smart_agents = [a for a in model.agents if a.agent_type == "smart"]
    
    if not smart_agents:
        print("❌ No smart agents found for testing")
        return False

    # Test decision making for first smart agent
    agent = smart_agents[0]
    
    print(f"Testing Smart Agent {agent.unique_id}:")
    print(f"  Initial wealth: ${agent.wealth:.2f}")
    print(f"  Initial inventory: {agent.inventory}")
    print(f"  Fair value belief: ${agent.fair_value_belief:.2f}")
    print(f"  Current market price: ${model.current_price:.2f}")

    # Test context preparation
    context = agent.prepare_market_context()
    print(f"  Market context prepared: {len(context)} fields")

    # Test AI decision (if API is available)
    if agent.api_available:
        print("  Making AI decision...")
        try:
            decision = agent.make_smart_decision()
            print(f"  AI Decision: {decision}")
            print(f"  Last confidence: {agent.last_confidence:.2f}")
            print(f"  Last reasoning: {agent.last_reasoning[:100]}...")
        except Exception as e:
            print(f"  ⚠️  AI decision failed: {e}")
    else:
        print("  API not available, testing fallback...")
        decision = agent.make_smart_decision()
        print(f"  Fallback decision: {decision}")

    print("✅ Smart agent decision test completed!")
    return True

def test_simulation_with_smart_agents():
    """Test running a simulation with smart agents."""
    print("\n🧪 Testing Simulation with Smart Agents")
    print("=" * 60)

    # Create model
    model = MarketModel(
        num_basic_agents=8,
        num_smart_agents=2,  # 10% smart agents
        width=10,
        height=10,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )

    print(f"Running simulation with {len(model.agents)} agents...")

    # Run simulation for 10 steps
    for step in range(10):
        print(f"  Step {step + 1}:", end=" ")
        
        try:
            model.step()
            
            # Get metrics
            price = model.current_price
            volume = model.transaction_volume
            basic_wealth = model.get_avg_wealth_by_type("basic")
            smart_wealth = model.get_avg_wealth_by_type("smart")
            
            print(f"Price: ${price:.2f}, Volume: {volume}, Basic: ${basic_wealth:.0f}, Smart: ${smart_wealth:.0f}")
            
        except Exception as e:
            print(f"❌ Step failed: {e}")
            return False

    # Get smart agent performance metrics
    smart_agents = [a for a in model.agents if a.agent_type == "smart"]
    
    print("\nSmart Agent Performance:")
    for agent in smart_agents:
        metrics = agent.get_ai_performance_metrics()
        print(f"  Agent {agent.unique_id}:")
        print(f"    API calls: {metrics['api_calls_made']}")
        print(f"    API success rate: {metrics['api_success_rate']:.1%}")
        print(f"    AI decisions: {metrics['ai_decisions']}")
        print(f"    Fallback decisions: {metrics['fallback_decisions']}")
        print(f"    Financial performance: {metrics['financial_performance']:+.1%}")
        print(f"    Average confidence: {metrics['avg_confidence']:.2f}")

    print("✅ Simulation with smart agents completed!")
    return True

def test_performance_comparison():
    """Test performance comparison between basic and smart agents."""
    print("\n🧪 Testing Performance Comparison")
    print("=" * 60)

    # Create model
    model = MarketModel(
        num_basic_agents=18,
        num_smart_agents=2,
        width=15,
        height=15,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )

    print(f"Running extended simulation for performance comparison...")
    print(f"Basic agents: {model.num_basic_agents}, Smart agents: {model.num_smart_agents}")

    # Run for 25 steps
    for step in range(25):
        if step % 5 == 0:
            print(f"  Progress: {step}/25 steps")
        model.step()

    # Final performance analysis
    basic_agents = [a for a in model.agents if a.agent_type == "basic"]
    smart_agents = [a for a in model.agents if a.agent_type == "smart"]

    basic_performance = [a.get_performance() for a in basic_agents]
    smart_performance = [a.get_performance() for a in smart_agents]

    avg_basic = sum(basic_performance) / len(basic_performance) if basic_performance else 0
    avg_smart = sum(smart_performance) / len(smart_performance) if smart_performance else 0

    print(f"\nFINAL PERFORMANCE COMPARISON:")
    print(f"  Average Basic Agent Performance: {avg_basic:+.1%}")
    print(f"  Average Smart Agent Performance: {avg_smart:+.1%}")
    print(f"  Smart Agent Advantage: {(avg_smart - avg_basic):+.1%}")

    # Smart agent specific metrics
    total_api_calls = sum(a.api_calls_made for a in smart_agents)
    total_ai_decisions = sum(a.ai_decisions for a in smart_agents)
    total_fallbacks = sum(a.fallback_decisions for a in smart_agents)

    print(f"\nSMART AGENT METRICS:")
    print(f"  Total API calls: {total_api_calls}")
    print(f"  AI decisions: {total_ai_decisions}")
    print(f"  Fallback decisions: {total_fallbacks}")
    print(f"  AI decision rate: {total_ai_decisions/(total_ai_decisions + total_fallbacks):.1%}")

    print("✅ Performance comparison completed!")
    return True

if __name__ == "__main__":
    """Run all smart agent tests."""
    print("🚀 Starting Smart Agent Test Suite")
    print("=" * 80)

    try:
        # Run tests
        test_smart_agent_creation()
        test_smart_agent_decisions()
        test_simulation_with_smart_agents()
        test_performance_comparison()

        print("\n🎉 All Smart Agent tests completed successfully!")
        print("\n📊 Ready for Phase 2C implementation!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
