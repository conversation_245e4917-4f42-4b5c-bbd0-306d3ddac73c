"""
Simple test for rate limiting functionality.
"""

import os
import sys
from dotenv import load_dotenv

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from market_sim.utils.rate_limiter import get_rate_limiter, configure_rate_limiter

def test_basic_rate_limiting():
    """Test basic rate limiting functionality."""
    print("🧪 Testing Basic Rate Limiting")
    print("=" * 40)

    # Configure rate limiter
    rate_limiter = configure_rate_limiter(
        max_requests_per_minute=15,
        min_delay_between_calls=2.0,
        cache_duration=5,
        enable_caching=True
    )

    print(f"✅ Rate limiter configured")
    print(f"   Max requests/min: {rate_limiter.max_requests_per_minute}")
    print(f"   Min delay: {rate_limiter.min_delay_between_calls}s")
    print(f"   Caching enabled: {rate_limiter.enable_caching}")

    # Test can_make_call
    can_call, wait_time = rate_limiter.can_make_call()
    print(f"\n📞 API Call Test:")
    print(f"   Can make call: {can_call}")
    print(f"   Wait time: {wait_time:.2f}s")

    # Test caching
    print(f"\n💾 Caching Test:")
    cached = rate_limiter.get_cached_decision(123, "test_hash", 1)
    print(f"   Cache miss (expected): {cached is None}")

    rate_limiter.cache_decision(123, "test_hash", "buy", 0.8, "test", 1)
    cached = rate_limiter.get_cached_decision(123, "test_hash", 1)
    print(f"   Cache hit (expected): {cached is not None}")

    # Test usage stats
    stats = rate_limiter.get_usage_stats()
    print(f"\n📊 Usage Statistics:")
    print(f"   Total calls: {stats['total_calls_made']}")
    print(f"   Cache hits: {stats['total_cache_hits']}")
    print(f"   Can call now: {stats['can_make_call_now']}")

    print(f"\n✅ Rate limiting test completed successfully!")

if __name__ == "__main__":
    load_dotenv()
    test_basic_rate_limiting()
