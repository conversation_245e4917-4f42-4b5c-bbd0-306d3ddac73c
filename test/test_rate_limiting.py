"""
Test script for Rate Limiting and API Quota Management.

This script tests:
1. Rate limiter functionality and quota enforcement
2. Smart agent behavior under rate limiting
3. Caching system effectiveness
4. Simulation speed controls
5. API usage optimization
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add parent directory to path so we can import market_sim
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from market_sim.model import MarketModel
from market_sim.utils.rate_limiter import get_rate_limiter, configure_rate_limiter

def test_rate_limiter_basic():
    """Test basic rate limiter functionality."""
    print("🧪 Testing Rate Limiter Basic Functionality")
    print("=" * 60)

    # Configure a strict rate limiter for testing
    rate_limiter = configure_rate_limiter(
        max_requests_per_minute=5,  # Very low for testing
        min_delay_between_calls=1.0,  # 1 second delay
        cache_duration=3,
        enable_caching=True
    )

    print(f"✅ Rate limiter configured: {rate_limiter.max_requests_per_minute} req/min")

    # Test immediate calls
    for i in range(3):
        can_call, wait_time = rate_limiter.can_make_call()
        print(f"  Call {i+1}: Can call: {can_call}, Wait time: {wait_time:.2f}s")
        
        if can_call:
            rate_limiter.record_call(agent_id=1, success=True, tokens_used=100)
            time.sleep(0.1)  # Small delay to avoid exact timing issues
        else:
            print(f"    Rate limited! Need to wait {wait_time:.2f} seconds")

    # Test quota enforcement
    print("\n📊 Testing quota enforcement...")
    for i in range(5):
        can_call, wait_time = rate_limiter.can_make_call()
        if can_call:
            rate_limiter.record_call(agent_id=1, success=True)
            print(f"  API call {i+1} allowed")
        else:
            print(f"  API call {i+1} blocked - quota exceeded")
            break

    # Get usage stats
    stats = rate_limiter.get_usage_stats()
    print(f"\n📈 Usage Statistics:")
    print(f"  Total calls: {stats['total_calls_made']}")
    print(f"  Calls last minute: {stats['calls_last_minute']}")
    print(f"  Can make call now: {stats['can_make_call_now']}")
    print(f"  Time until next call: {stats['time_until_next_call']:.2f}s")

    return True

def test_caching_system():
    """Test the decision caching system."""
    print("\n🧪 Testing Decision Caching System")
    print("=" * 60)

    rate_limiter = get_rate_limiter()
    
    # Test cache miss and hit
    agent_id = 123
    context_hash = "test_context_abc123"
    current_step = 10
    
    # Should be cache miss
    cached = rate_limiter.get_cached_decision(agent_id, context_hash, current_step)
    print(f"✅ Cache miss test: {cached is None}")
    
    # Cache a decision
    rate_limiter.cache_decision(
        agent_id, context_hash, "buy", 0.85, "Test reasoning", current_step
    )
    print("✅ Decision cached")
    
    # Should be cache hit
    cached = rate_limiter.get_cached_decision(agent_id, context_hash, current_step)
    print(f"✅ Cache hit test: {cached is not None}")
    
    if cached:
        decision, confidence, reasoning = cached
        print(f"  Cached decision: {decision} (confidence: {confidence})")
        print(f"  Cached reasoning: {reasoning}")
    
    # Test cache expiration
    old_step = current_step - 10  # Very old
    expired = rate_limiter.get_cached_decision(agent_id, context_hash, old_step)
    print(f"✅ Cache expiration test: {expired is None}")
    
    return True

def test_smart_agents_with_rate_limiting():
    """Test smart agents under rate limiting conditions."""
    print("\n🧪 Testing Smart Agents with Rate Limiting")
    print("=" * 60)

    # Configure moderate rate limiting
    configure_rate_limiter(
        max_requests_per_minute=10,
        min_delay_between_calls=2.0,
        cache_duration=5,
        enable_caching=True
    )

    # Create model with smart agents
    model = MarketModel(
        num_basic_agents=5,
        num_smart_agents=3,
        width=10,
        height=10,
        initial_price=100.0,
        initial_wealth=1000.0,
        initial_inventory=10
    )

    print(f"✅ Model created with {len(model.agents)} agents")

    smart_agents = [a for a in model.agents if a.agent_type == "smart"]
    print(f"✅ Found {len(smart_agents)} smart agents")

    # Run simulation for several steps
    print("\n🔄 Running simulation with rate limiting...")
    
    for step in range(8):
        print(f"  Step {step + 1}:", end=" ")
        
        start_time = time.time()
        model.step()
        step_time = time.time() - start_time
        
        # Collect metrics
        rate_limiter = get_rate_limiter()
        stats = rate_limiter.get_usage_stats()
        
        total_ai_decisions = sum(a.ai_decisions for a in smart_agents)
        total_fallbacks = sum(a.fallback_decisions for a in smart_agents)
        
        print(f"Time: {step_time:.2f}s, API calls: {stats['calls_last_minute']}, "
              f"AI decisions: {total_ai_decisions}, Fallbacks: {total_fallbacks}")

    # Final performance analysis
    print(f"\n📊 Final Rate Limiting Analysis:")
    stats = rate_limiter.get_usage_stats()
    
    print(f"  Total API calls made: {stats['total_calls_made']}")
    print(f"  Total cache hits: {stats['total_cache_hits']}")
    print(f"  Cache hit rate: {stats['cache_hit_rate']:.1%}")
    print(f"  Calls in last minute: {stats['calls_last_minute']}")
    print(f"  Can make call now: {stats['can_make_call_now']}")

    # Smart agent specific metrics
    for i, agent in enumerate(smart_agents):
        metrics = agent.get_ai_performance_metrics()
        print(f"  Smart Agent {i+1}:")
        print(f"    AI decisions: {metrics['ai_decisions']}")
        print(f"    Fallback decisions: {metrics['fallback_decisions']}")
        print(f"    AI decision rate: {metrics['ai_decision_rate']:.1%}")

    return True

def test_simulation_speed_control():
    """Test simulation speed control functionality."""
    print("\n🧪 Testing Simulation Speed Control")
    print("=" * 60)

    # Test different speeds
    speeds = [0.01, 0.1, 0.5, 1.0]
    
    for speed in speeds:
        print(f"Testing speed: {speed}s per step")
        
        # Create a small model for speed testing
        model = MarketModel(
            num_basic_agents=3,
            num_smart_agents=1,
            width=5,
            height=5
        )
        
        # Time a few steps
        start_time = time.time()
        for _ in range(3):
            model.step()
            time.sleep(speed)  # Simulate the speed control
        
        total_time = time.time() - start_time
        expected_time = speed * 3
        
        print(f"  Expected time: {expected_time:.2f}s, Actual time: {total_time:.2f}s")
        print(f"  Speed accuracy: {abs(total_time - expected_time) < 0.5}")

    return True

def test_api_quota_exhaustion_recovery():
    """Test behavior when API quota is exhausted and recovery."""
    print("\n🧪 Testing API Quota Exhaustion and Recovery")
    print("=" * 60)

    # Configure very strict limits
    configure_rate_limiter(
        max_requests_per_minute=3,  # Very low
        min_delay_between_calls=1.0,
        cache_duration=2,
        enable_caching=True
    )

    model = MarketModel(
        num_basic_agents=2,
        num_smart_agents=2,
        width=5,
        height=5
    )

    smart_agents = [a for a in model.agents if a.agent_type == "smart"]
    rate_limiter = get_rate_limiter()

    print("🔄 Exhausting API quota...")
    
    # Run until quota is exhausted
    for step in range(10):
        model.step()
        stats = rate_limiter.get_usage_stats()
        
        if not stats['can_make_call_now']:
            print(f"  ✅ Quota exhausted at step {step + 1}")
            print(f"  Wait time: {stats['time_until_next_call']:.2f}s")
            break
    
    # Check fallback behavior
    total_fallbacks = sum(a.fallback_decisions for a in smart_agents)
    total_ai_decisions = sum(a.ai_decisions for a in smart_agents)
    
    print(f"  AI decisions made: {total_ai_decisions}")
    print(f"  Fallback decisions: {total_fallbacks}")
    print(f"  Fallback rate: {total_fallbacks / max(1, total_ai_decisions + total_fallbacks):.1%}")

    # Test recovery after waiting
    print("\n⏳ Testing recovery after quota reset...")
    time.sleep(2)  # Wait for quota to reset
    
    stats = rate_limiter.get_usage_stats()
    print(f"  Can make call after wait: {stats['can_make_call_now']}")
    
    return True

if __name__ == "__main__":
    """Run all rate limiting tests."""
    print("🚀 Starting Rate Limiting Test Suite")
    print("=" * 80)

    # Load environment
    load_dotenv()

    try:
        # Run all tests
        test_rate_limiter_basic()
        test_caching_system()
        test_smart_agents_with_rate_limiting()
        test_simulation_speed_control()
        test_api_quota_exhaustion_recovery()

        print("\n🎉 All Rate Limiting tests completed successfully!")
        print("\n📋 Summary of Rate Limiting Features:")
        print("  ✅ Global API rate limiting (15 req/min default)")
        print("  ✅ Configurable delays between API calls (2s default)")
        print("  ✅ Decision caching to reduce API usage")
        print("  ✅ Graceful fallback to basic agent logic")
        print("  ✅ Real-time API usage monitoring")
        print("  ✅ Simulation speed controls")
        print("  ✅ Quota exhaustion recovery")
        
        print("\n🎯 Ready for production use with API quota protection!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
