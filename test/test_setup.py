"""
Quick test to verify our setup is working correctly.
"""

import os
from dotenv import load_dotenv

def test_environment():
    """Test that our environment is set up correctly."""
    print("Testing environment setup...")

    # Test imports
    try:
        import mesa
        print(f"✓ Mesa imported successfully (version: {mesa.__version__})")
    except ImportError as e:
        print(f"✗ Mesa import failed: {e}")
        return False

    try:
        import google.generativeai as genai
        print("✓ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"✗ Google Generative AI import failed: {e}")
        return False

    try:
        import numpy as np
        print(f"✓ NumPy imported successfully (version: {np.__version__})")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False

    # Test environment variables
    load_dotenv()
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        print("✓ GEMINI_API_KEY found in environment")
        print(f"  Key starts with: {api_key[:10]}...")
    else:
        print("✗ GEMINI_API_KEY not found in environment")
        return False

    # Test our market model
    try:
        import sys
        import os as os_module
        sys.path.append(os_module.path.dirname(os_module.path.dirname(os_module.path.abspath(__file__))))
        from market_sim.model import MarketModel
        model = MarketModel(num_basic_agents=2, num_smart_agents=1)
        print("✓ MarketModel created successfully")
        print(f"  Initial price: ${model.current_price:.2f}")
        print(f"  Grid size: {model.width}x{model.height}")
    except Exception as e:
        print(f"✗ MarketModel creation failed: {e}")
        return False

    print("\n🎉 All tests passed! Environment is ready.")
    return True

if __name__ == "__main__":
    test_environment()
